@page "/login"
@model EmployeeTrafficControl.Web.Pages.Account.LoginModel
@{
    ViewData["Title"] = "ورود به سیستم";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="logo-container">
                <i class="bi bi-shield-check text-primary" style="font-size: 3rem;"></i>
            </div>
            <h2 class="login-title">سیستم کنترل تردد کارمندان</h2>
            <p class="login-subtitle">لطفاً اطلاعات خود را وارد کنید</p>
        </div>

        <form method="post" class="login-form">
            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

            <div class="form-group">
                <label asp-for="Username" class="form-label">
                    <i class="bi bi-person"></i>
                    نام کاربری
                </label>
                <input asp-for="Username" class="form-control" placeholder="نام کاربری خود را وارد کنید" autocomplete="username" />
                <span asp-validation-for="Username" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password" class="form-label">
                    <i class="bi bi-lock"></i>
                    رمز عبور
                </label>
                <div class="password-input-container">
                    <input asp-for="Password" type="password" class="form-control" placeholder="رمز عبور خود را وارد کنید" autocomplete="current-password" />
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <i class="bi bi-eye" id="password-toggle-icon"></i>
                    </button>
                </div>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input asp-for="RememberMe" class="form-check-input" type="checkbox" />
                    <label asp-for="RememberMe" class="form-check-label">
                        مرا به خاطر بسپار
                    </label>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-login">
                <i class="bi bi-box-arrow-in-right"></i>
                ورود به سیستم
            </button>
        </form>

        <div class="login-footer">
            <div class="system-info">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i>
                    در صورت فراموشی رمز عبور، با مدیر سیستم تماس بگیرید
                </small>
            </div>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;
        animation: slideUp 0.6s ease-out;
    }

    @@keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .logo-container {
        margin-bottom: 20px;
    }

    .login-title {
        color: #333;
        font-weight: 700;
        margin-bottom: 10px;
        font-size: 1.5rem;
    }

    .login-subtitle {
        color: #666;
        margin-bottom: 0;
        font-size: 0.95rem;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-control {
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .password-input-container {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .password-toggle:hover {
        color: #333;
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-check-input {
        margin: 0;
    }

    .btn-login {
        width: 100%;
        padding: 12px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .login-footer {
        margin-top: 30px;
        text-align: center;
    }

    .system-info {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border-right: 4px solid #667eea;
    }

    .alert {
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .text-danger {
        font-size: 0.875rem;
        margin-top: 5px;
        display: block;
    }
</style>

<script>
    function togglePassword() {
        const passwordInput = document.querySelector('input[name="Password"]');
        const toggleIcon = document.getElementById('password-toggle-icon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }

    // Auto-focus on username field
    document.addEventListener('DOMContentLoaded', function() {
        const usernameInput = document.querySelector('input[name="Username"]');
        if (usernameInput) {
            usernameInput.focus();
        }
    });
</script>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
