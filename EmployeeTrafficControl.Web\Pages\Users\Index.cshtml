@page
@model EmployeeTrafficControl.Web.Pages.Users.IndexModel
@{
    ViewData["Title"] = "لیست کاربران";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>لیست کاربران</h1>
        <a asp-page="Create" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> افزودن کاربر جدید
        </a>
    </div>
</div>

@if (Model.Users == null || !Model.Users.Any())
{
    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle"></i>
        <p class="mb-0">هیچ کاربری یافت نشد.</p>
        <a asp-page="Create" class="btn btn-primary mt-2">افزودن اولین کاربر</a>
    </div>
}
else
{
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>نام کاربری</th>
                            <th>نقش</th>
                            <th>کارمند مرتبط</th>
                            <th>ساختمان</th>
                            <th class="text-center">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr>
                                <td>
                                    <strong>@user.Username</strong>
                                </td>
                                <td>
                                    @if (user.Role == "Admin")
                                    {
                                        <span class="badge bg-danger">مدیر</span>
                                    }
                                    else if (user.Role == "Manager")
                                    {
                                        <span class="badge bg-warning">مدیر ساختمان</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-info">@user.Role</span>
                                    }
                                </td>
                                <td>
                                    @if (user.Employee != null)
                                    {
                                        <span>@user.Employee.FirstName @user.Employee.LastName</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">بدون کارمند</span>
                                    }
                                </td>
                                <td>
                                    @if (user.Building != null)
                                    {
                                        <span class="badge bg-secondary">@user.Building.Name</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">همه ساختمان‌ها</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a asp-page="Edit" asp-route-id="@user.UserId" class="btn btn-sm btn-outline-primary" title="ویرایش">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-page="Details" asp-route-id="@user.UserId" class="btn btn-sm btn-outline-info" title="جزئیات">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <form asp-page="Delete" asp-route-id="@user.UserId" method="post" class="d-inline" 
                                              onsubmit="return confirmDelete('آیا مطمئن هستید که می‌خواهید این کاربر را حذف کنید؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <p class="text-muted">
                    نمایش @Model.Users.Count مورد
                </p>
            </div>
            <div class="col-md-6 text-end">
                <a asp-page="Create" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> افزودن کاربر جدید
                </a>
            </div>
        </div>
    </div>
}
