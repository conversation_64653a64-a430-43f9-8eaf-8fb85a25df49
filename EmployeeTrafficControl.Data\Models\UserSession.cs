using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Models
{
    public class UserSession
    {
        [Key]
        public int SessionId { get; set; }

        [Display(Name = "کاربر")]
        [Required]
        public int UserId { get; set; }

        [Display(Name = "توکن نشست")]
        [Required]
        [MaxLength(500)]
        public string SessionToken { get; set; } = default!;

        [Display(Name = "زمان ورود")]
        public DateTime LoginTime { get; set; } = DateTime.Now;

        [Display(Name = "زمان آخرین فعالیت")]
        public DateTime LastActivity { get; set; } = DateTime.Now;

        [Display(Name = "زمان خروج")]
        public DateTime? LogoutTime { get; set; }

        [Display(Name = "آدرس IP")]
        [MaxLength(45)] // IPv6 support
        public string? IpAddress { get; set; }

        [Display(Name = "مرورگر")]
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "منقضی شده")]
        public bool IsExpired { get; set; } = false;

        [Display(Name = "زمان انقضا")]
        public DateTime ExpiryTime { get; set; } = DateTime.Now.AddHours(8); // 8 hours default

        // Navigation property
        [Display(Name = "کاربر")]
        public User User { get; set; } = default!;

        // Helper methods
        public bool IsSessionValid()
        {
            return IsActive && !IsExpired && DateTime.Now < ExpiryTime;
        }

        public void ExtendSession(int hours = 8)
        {
            ExpiryTime = DateTime.Now.AddHours(hours);
            LastActivity = DateTime.Now;
        }

        public void EndSession()
        {
            IsActive = false;
            LogoutTime = DateTime.Now;
        }
    }
}
