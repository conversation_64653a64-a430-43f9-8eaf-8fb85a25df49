﻿using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmployeeTrafficControl.Services
{
    public class CarService
    {
        private readonly ApplicationDbContext _context;

        public CarService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Retrieves all cars from the database.
        /// </summary>
        /// <returns>A list of Car objects.</returns>
        public async Task<List<Car>> GetAllCarsAsync()
        {
            return await _context.Cars
                                .Include(c => c.Building)
                                .OrderBy(c => c.PlateNumber)
                                .ToListAsync();
        }

        /// <summary>
        /// Retrieves a single car by its ID.
        /// </summary>
        /// <param name="carId">The ID of the car to retrieve.</param>
        /// <returns>The Car object if found, otherwise null.</returns>
        public async Task<Car?> GetCarByIdAsync(int carId)
        {
            return await _context.Cars
                                .Include(c => c.Building)
                                .FirstOrDefaultAsync(c => c.CarId == carId);
        }

        /// <summary>
        /// Adds a new car to the database.
        /// </summary>
        /// <param name="car">The Car object to add.</param>
        /// <returns>The added Car object.</returns>
        public async Task<Car> AddCarAsync(Car car)
        {
            _context.Cars.Add(car);
            await _context.SaveChangesAsync();
            return car;
        }

        /// <summary>
        /// Updates an existing car in the database.
        /// </summary>
        /// <param name="car">The Car object with updated information.</param>
        /// <returns>True if the update was successful, otherwise false.</returns>
        public async Task<bool> UpdateCarAsync(Car car)
        {
            var existingCar = await _context.Cars.FindAsync(car.CarId);
            if (existingCar == null)
            {
                return false;
            }

            existingCar.PlateNumber = car.PlateNumber;
            existingCar.Model = car.Model;
            existingCar.Color = car.Color;
            existingCar.Type = car.Type;
            existingCar.PassengerCapacity = car.PassengerCapacity;

            try
            {
                await _context.SaveChangesAsync();
                return true;
            }
            catch (DbUpdateConcurrencyException)
            {
                // Handle concurrency conflicts if needed
                return false;
            }
        }

        /// <summary>
        /// Deletes a car from the database by its ID.
        /// </summary>
        /// <param name="carId">The ID of the car to delete.</param>
        /// <returns>True if the deletion was successful, otherwise false.</returns>
        public async Task<bool> DeleteCarAsync(int carId)
        {
            var carToDelete = await _context.Cars.FindAsync(carId);
            if (carToDelete == null)
            {
                return false;
            }

            // Check for related data (e.g., CarTrafficLogs)
            var hasTrafficLogs = await _context.CarTrafficLogs.AnyAsync(ctl => ctl.CarId == carId);
            if (hasTrafficLogs)
            {
                Console.WriteLine("Cannot delete car: Related traffic logs exist.");
                return false;
            }

            _context.Cars.Remove(carToDelete);
            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// Checks if a car with the given plate number already exists (case-insensitive).
        /// </summary>
        /// <param name="plateNumber">The plate number to check.</param>
        /// <param name="excludeCarId">Optional: An ID to exclude during the check (useful for updates).</param>
        /// <returns>True if a car with the plate number exists, otherwise false.</returns>
        public async Task<bool> CarExistsAsync(string plateNumber, int? excludeCarId = null)
        {
            if (excludeCarId.HasValue)
            {
                return await _context.Cars.AnyAsync(c =>
                    c.PlateNumber.ToLower() == plateNumber.ToLower() && c.CarId != excludeCarId.Value);
            }
            return await _context.Cars.AnyAsync(c => c.PlateNumber.ToLower() == plateNumber.ToLower());
        }
    }
}