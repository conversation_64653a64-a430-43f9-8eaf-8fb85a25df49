﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class car_buildingid : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CarTrafficLogs_Cars_CarId",
                table: "CarTrafficLogs");

            migrationBuilder.AlterColumn<DateTime>(
                name: "EntryTime",
                table: "CarTrafficLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "DriverEmployeeId",
                table: "CarTrafficLogs",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "CarId",
                table: "CarTrafficLogs",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<string>(
                name: "GuestName",
                table: "CarTrafficLogs",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PlateNumber",
                table: "CarTrafficLogs",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "CarTrafficLogs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "BuildingId",
                table: "Cars",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_Cars_BuildingId",
                table: "Cars",
                column: "BuildingId");

            migrationBuilder.AddForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars",
                column: "BuildingId",
                principalTable: "Buildings",
                principalColumn: "BuildingId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CarTrafficLogs_Cars_CarId",
                table: "CarTrafficLogs",
                column: "CarId",
                principalTable: "Cars",
                principalColumn: "CarId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars");

            migrationBuilder.DropForeignKey(
                name: "FK_CarTrafficLogs_Cars_CarId",
                table: "CarTrafficLogs");

            migrationBuilder.DropIndex(
                name: "IX_Cars_BuildingId",
                table: "Cars");

            migrationBuilder.DropColumn(
                name: "GuestName",
                table: "CarTrafficLogs");

            migrationBuilder.DropColumn(
                name: "PlateNumber",
                table: "CarTrafficLogs");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "CarTrafficLogs");

            migrationBuilder.DropColumn(
                name: "BuildingId",
                table: "Cars");

            migrationBuilder.AlterColumn<DateTime>(
                name: "EntryTime",
                table: "CarTrafficLogs",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AlterColumn<int>(
                name: "DriverEmployeeId",
                table: "CarTrafficLogs",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "CarId",
                table: "CarTrafficLogs",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CarTrafficLogs_Cars_CarId",
                table: "CarTrafficLogs",
                column: "CarId",
                principalTable: "Cars",
                principalColumn: "CarId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
