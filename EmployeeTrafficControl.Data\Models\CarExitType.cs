using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Models
{
    public enum CarExitType
    {
        [Display(Name = "اداری")]
        Administrative = 1,

        [Display(Name = "ماموریت")]
        Mission = 2,

        [Display(Name = "پولرسانی")]
        MoneyTransport = 3
    }

    public static class CarExitTypeExtensions
    {
        public static string GetDisplayName(this CarExitType exitType)
        {
            return exitType switch
            {
                CarExitType.Administrative => "اداری",
                CarExitType.Mission => "ماموریت",
                CarExitType.MoneyTransport => "پولرسانی",
                _ => "نامشخص"
            };
        }

        public static string GetBadgeClass(this CarExitType exitType)
        {
            return exitType switch
            {
                CarExitType.Administrative => "bg-primary",
                CarExitType.Mission => "bg-info",
                CarExitType.MoneyTransport => "bg-warning",
                _ => "bg-secondary"
            };
        }

        public static string GetIcon(this CarExitType exitType)
        {
            return exitType switch
            {
                CarExitType.Administrative => "bi-building",
                CarExitType.Mission => "bi-briefcase",
                CarExitType.MoneyTransport => "bi-cash-coin",
                _ => "bi-question-circle"
            };
        }
    }
}
