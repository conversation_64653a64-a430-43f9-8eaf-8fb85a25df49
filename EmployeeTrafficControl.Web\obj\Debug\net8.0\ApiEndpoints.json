[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_2", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterEntry", "RelativePath": "api/traffic/register-entry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterEntryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterExit", "RelativePath": "api/traffic/register-exit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterExitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterGroupExit", "RelativePath": "api/traffic/register-group-exit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterGroupExitRequest", "IsRequired": true}], "ReturnTypes": []}]