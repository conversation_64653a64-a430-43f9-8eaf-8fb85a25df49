[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_2", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "GetAvailablePassengers", "RelativePath": "api/traffic/get-available-passengers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "exitType", "Type": "System.String", "IsRequired": false}, {"Name": "buildingId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterCarEntry", "RelativePath": "api/traffic/register-car-entry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterCarEntryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterCarExit", "RelativePath": "api/traffic/register-car-exit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterCarExitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterEntry", "RelativePath": "api/traffic/register-entry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterEntryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterExit", "RelativePath": "api/traffic/register-exit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterExitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "EmployeeTrafficControl.Web.Controllers.TrafficApiController", "Method": "RegisterGroupExit", "RelativePath": "api/traffic/register-group-exit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "EmployeeTrafficControl.Web.Controllers.RegisterGroupExitRequest", "IsRequired": true}], "ReturnTypes": []}]