using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using System.Security.Cryptography;
using System.Text;

namespace EmployeeTrafficControl.Services
{
    public class AuthenticationService
    {
        private readonly ApplicationDbContext _context;

        public AuthenticationService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// احراز هویت کاربر
        /// </summary>
        /// <param name="username">نام کاربری</param>
        /// <param name="password">رمز عبور</param>
        /// <param name="ipAddress">آدرس IP</param>
        /// <param name="userAgent">اطلاعات مرورگر</param>
        /// <returns>نشست کاربر در صورت موفقیت، null در صورت شکست</returns>
        public async Task<UserSession?> LoginAsync(string username, string password, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                // یافتن کاربر
                var user = await _context.Users
                                         .Include(u => u.Employee)
                                         .Include(u => u.Building)
                                         .FirstOrDefaultAsync(u => u.Username.ToLower() == username.ToLower());

                if (user == null)
                    return null;

                // بررسی رمز عبور
                if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                    return null;

                // پایان دادن به نشست‌های قبلی فعال
                await EndActiveSessionsAsync(user.UserId);

                // ایجاد نشست جدید
                var sessionToken = GenerateSessionToken();
                var session = new UserSession
                {
                    UserId = user.UserId,
                    SessionToken = sessionToken,
                    LoginTime = DateTime.Now,
                    LastActivity = DateTime.Now,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    IsActive = true,
                    ExpiryTime = DateTime.Now.AddHours(8) // 8 ساعت اعتبار
                };

                _context.UserSessions.Add(session);
                await _context.SaveChangesAsync();

                // بارگذاری اطلاعات کاربر
                session.User = user;
                return session;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Login error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// خروج کاربر
        /// </summary>
        /// <param name="sessionToken">توکن نشست</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> LogoutAsync(string sessionToken)
        {
            try
            {
                var session = await _context.UserSessions
                                           .FirstOrDefaultAsync(s => s.SessionToken == sessionToken && s.IsActive);

                if (session != null)
                {
                    session.EndSession();
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Logout error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اعتبارسنجی نشست
        /// </summary>
        /// <param name="sessionToken">توکن نشست</param>
        /// <returns>نشست معتبر یا null</returns>
        public async Task<UserSession?> ValidateSessionAsync(string sessionToken)
        {
            try
            {
                var session = await _context.UserSessions
                                           .Include(s => s.User)
                                           .ThenInclude(u => u.Employee)
                                           .Include(s => s.User)
                                           .ThenInclude(u => u.Building)
                                           .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

                if (session == null || !session.IsSessionValid())
                {
                    if (session != null)
                    {
                        session.IsExpired = true;
                        await _context.SaveChangesAsync();
                    }
                    return null;
                }

                // به‌روزرسانی آخرین فعالیت
                session.LastActivity = DateTime.Now;
                await _context.SaveChangesAsync();

                return session;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Session validation error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تمدید نشست
        /// </summary>
        /// <param name="sessionToken">توکن نشست</param>
        /// <param name="hours">تعداد ساعت تمدید</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> ExtendSessionAsync(string sessionToken, int hours = 8)
        {
            try
            {
                var session = await _context.UserSessions
                                           .FirstOrDefaultAsync(s => s.SessionToken == sessionToken && s.IsActive);

                if (session != null && session.IsSessionValid())
                {
                    session.ExtendSession(hours);
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Session extension error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// پایان دادن به تمام نشست‌های فعال کاربر
        /// </summary>
        /// <param name="userId">شناسه کاربر</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> EndActiveSessionsAsync(int userId)
        {
            try
            {
                var activeSessions = await _context.UserSessions
                                                  .Where(s => s.UserId == userId && s.IsActive)
                                                  .ToListAsync();

                foreach (var session in activeSessions)
                {
                    session.EndSession();
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"End sessions error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// پاک‌سازی نشست‌های منقضی شده
        /// </summary>
        /// <returns>تعداد نشست‌های پاک شده</returns>
        public async Task<int> CleanupExpiredSessionsAsync()
        {
            try
            {
                var expiredSessions = await _context.UserSessions
                                                   .Where(s => s.ExpiryTime < DateTime.Now || !s.IsActive)
                                                   .ToListAsync();

                foreach (var session in expiredSessions)
                {
                    session.IsExpired = true;
                    session.IsActive = false;
                }

                await _context.SaveChangesAsync();
                return expiredSessions.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Cleanup sessions error: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// تولید توکن نشست
        /// </summary>
        /// <returns>توکن منحصر به فرد</returns>
        private string GenerateSessionToken()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[32];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes) + "_" + DateTime.Now.Ticks;
            }
        }

        /// <summary>
        /// دریافت کاربر از طریق توکن نشست
        /// </summary>
        /// <param name="sessionToken">توکن نشست</param>
        /// <returns>کاربر یا null</returns>
        public async Task<User?> GetUserBySessionTokenAsync(string sessionToken)
        {
            var session = await ValidateSessionAsync(sessionToken);
            return session?.User;
        }

        /// <summary>
        /// بررسی دسترسی کاربر به ساختمان
        /// </summary>
        /// <param name="userId">شناسه کاربر</param>
        /// <param name="buildingId">شناسه ساختمان</param>
        /// <returns>true اگر دسترسی داشته باشد</returns>
        public async Task<bool> HasBuildingAccessAsync(int userId, int buildingId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return true;

            // اگر کاربر به ساختمان خاصی محدود نشده، به همه دسترسی دارد
            if (!user.BuildingId.HasValue)
                return true;

            // بررسی دسترسی به ساختمان مشخص
            return user.BuildingId == buildingId;
        }
    }
}
