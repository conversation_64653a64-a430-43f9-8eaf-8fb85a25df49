@page "/traffic/car-exit"
@model EmployeeTrafficControl.Web.Pages.Traffic.CarExitModel
@{
    ViewData["Title"] = "خروج خودرو";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-car-front-fill"></i> خروج خودرو</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">ثبت خروج خودروهای کارمندان و مهمانان از پارکینگ</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">جستجو</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="شماره پلاک، راننده یا مهمان" />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع خودرو</label>
                        <select name="CarType" class="form-select">
                            <option value="">همه انواع</option>
                            <option value="employee" selected="@(Model.CarType == "employee")">خودرو کارمند</option>
                            <option value="guest" selected="@(Model.CarType == "guest")">خودرو مهمان</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">مدت حضور</label>
                        <select name="DurationFilter" class="form-select">
                            <option value="">همه</option>
                            <option value="short" selected="@(Model.DurationFilter == "short")">کمتر از 2 ساعت</option>
                            <option value="medium" selected="@(Model.DurationFilter == "medium")">2-8 ساعت</option>
                            <option value="long" selected="@(Model.DurationFilter == "long")">بیش از 8 ساعت</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-total">
            <div class="stats-icon">
                <i class="bi bi-car-front"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TotalCarsInside</h3>
                <p>کل خودروها داخل</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-employee">
            <div class="stats-icon">
                <i class="bi bi-person-badge"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeeCarsInside</h3>
                <p>خودرو کارمندان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-guest">
            <div class="stats-icon">
                <i class="bi bi-person-plus"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.GuestCarsInside</h3>
                <p>خودرو مهمانان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-today-exit">
            <div class="stats-icon">
                <i class="bi bi-calendar-day"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TodayExits</h3>
                <p>خروج امروز</p>
            </div>
        </div>
    </div>
</div>

<!-- عملیات گروهی -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> عملیات گروهی
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button type="button" class="btn btn-success w-100" onclick="selectAllCars()">
                            <i class="bi bi-check-all"></i> انتخاب همه خودروها
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-warning w-100" onclick="selectLongStayCars()">
                            <i class="bi bi-clock"></i> انتخاب خودروهای طولانی مدت
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-danger w-100" onclick="registerSelectedExits()" id="exitBtn" disabled>
                            <i class="bi bi-car-front-fill"></i> ثبت خروج انتخاب شده‌ها
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- لیست خودروهای داخل پارکینگ -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> خودروهای داخل پارکینگ
                </h5>
                <span class="badge bg-success">@Model.CarsInside.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.CarsInside.Any())
                {
                    <form id="carExitForm">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll" class="form-check-input" onchange="toggleAllSelection()">
                                        </th>
                                        <th>شماره پلاک</th>
                                        <th>نوع</th>
                                        <th>راننده/مهمان</th>
                                        <th>ساختمان</th>
                                        <th>زمان ورود</th>
                                        <th>مدت حضور</th>
                                        <th>توضیحات</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var car in Model.CarsInside)
                                    {
                                        var duration = DateTime.Now - car.EntryTime;
                                        var hours = (int)duration.TotalHours;
                                        var minutes = duration.Minutes;
                                        var isLongStay = duration.TotalHours > 8;
                                        
                                        <tr class="car-row" data-car-id="@car.CarTrafficLogId" data-long-stay="@isLongStay.ToString().ToLower()">
                                            <td>
                                                <input type="checkbox" class="form-check-input car-checkbox" 
                                                       value="@car.CarTrafficLogId" onchange="updateExitButton()">
                                            </td>
                                            <td>
                                                <strong class="text-primary">@car.PlateNumber</strong>
                                            </td>
                                            <td>
                                                <span class="badge @(car.DriverEmployeeId != null ? "bg-info" : "bg-warning")">
                                                    @car.GetCarType()
                                                </span>
                                            </td>
                                            <td>
                                                @if (car.DriverEmployee != null)
                                                {
                                                    <div>
                                                        <strong>@car.GetDriverOrGuestName()</strong>
                                                        <br><small class="text-muted">@car.DriverEmployee.PersonnelCode</small>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="@(string.IsNullOrEmpty(car.GuestName) ? "text-muted" : "text-warning")">
                                                        @car.GetDriverOrGuestName()
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @if (car.Building != null)
                                                {
                                                    <span class="badge bg-secondary">@car.Building.Name</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="text-success">@car.EntryTime.ToString("HH:mm")</span>
                                                <br><small class="text-muted">@car.EntryTime.ToString("yyyy/MM/dd")</small>
                                            </td>
                                            <td>
                                                <span class="text-info @(isLongStay ? "fw-bold" : "")">@hours:@minutes.ToString("D2")</span>
                                                @if (isLongStay)
                                                {
                                                    <br><small class="text-warning">طولانی مدت</small>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(car.Notes))
                                                {
                                                    <small class="text-muted">@car.Notes</small>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="registerSingleCarExit(@car.CarTrafficLogId, '@car.PlateNumber')">
                                                    <i class="bi bi-box-arrow-right"></i> ثبت خروج
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </form>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-car-front-fill" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">هیچ خودرویی در پارکینگ نیست</h5>
                        <p>همه خودروها خارج از پارکینگ هستند.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-total {
        border-right: 4px solid #6c757d;
    }
    .stats-total .stats-icon { color: #6c757d; }

    .stats-employee {
        border-right: 4px solid #007bff;
    }
    .stats-employee .stats-icon { color: #007bff; }

    .stats-guest {
        border-right: 4px solid #ffc107;
    }
    .stats-guest .stats-icon { color: #ffc107; }

    .stats-today-exit {
        border-right: 4px solid #dc3545;
    }
    .stats-today-exit .stats-icon { color: #dc3545; }

    .car-row:hover {
        background-color: #f8f9fa;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }
</style>

<script>
    function toggleAllSelection() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.car-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateExitButton();
    }

    function selectAllCars() {
        const checkboxes = document.querySelectorAll('.car-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        document.getElementById('selectAll').checked = true;
        updateExitButton();
    }

    function selectLongStayCars() {
        const checkboxes = document.querySelectorAll('.car-checkbox');
        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const isLongStay = row.getAttribute('data-long-stay') === 'true';
            checkbox.checked = isLongStay;
        });
        updateExitButton();
    }

    function updateExitButton() {
        const checkedBoxes = document.querySelectorAll('.car-checkbox:checked');
        const exitBtn = document.getElementById('exitBtn');

        if (checkedBoxes.length > 0) {
            exitBtn.disabled = false;
            exitBtn.innerHTML = `<i class="bi bi-car-front-fill"></i> ثبت خروج ${checkedBoxes.length} خودرو`;
        } else {
            exitBtn.disabled = true;
            exitBtn.innerHTML = '<i class="bi bi-car-front-fill"></i> ثبت خروج انتخاب شده‌ها';
        }
    }

    async function registerSingleCarExit(carTrafficLogId, plateNumber) {
        if (!confirm(`آیا مطمئن هستید که می‌خواهید خروج خودرو "${plateNumber}" را ثبت کنید؟`)) {
            return;
        }

        try {
            const response = await fetch('/api/traffic/register-car-exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    carTrafficLogId: carTrafficLogId
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'خروج خودرو با موفقیت ثبت شد');
                window.location.href = '/dashboard';
            } else {
                alert(result.message || 'خطا در ثبت خروج خودرو. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
        }
    }

    async function registerSelectedExits() {
        const checkedBoxes = document.querySelectorAll('.car-checkbox:checked');
        const carIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));

        if (carIds.length === 0) {
            alert('لطفاً حداقل یک خودرو انتخاب کنید.');
            return;
        }

        if (!confirm(`آیا مطمئن هستید که می‌خواهید خروج ${carIds.length} خودرو را ثبت کنید؟`)) {
            return;
        }

        const exitBtn = document.getElementById('exitBtn');
        const originalText = exitBtn.innerHTML;
        exitBtn.disabled = true;
        exitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> در حال ثبت...';

        try {
            const promises = carIds.map(carId =>
                fetch('/api/traffic/register-car-exit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        carTrafficLogId: carId
                    })
                })
            );

            const responses = await Promise.all(promises);
            const results = await Promise.all(responses.map(r => r.json()));

            const successCount = results.filter(r => r.success).length;

            if (successCount > 0) {
                alert(`خروج ${successCount} خودرو از ${carIds.length} خودرو با موفقیت ثبت شد`);
                window.location.href = '/dashboard';
            } else {
                alert('خطا در ثبت خروج خودروها. لطفاً دوباره تلاش کنید.');
                exitBtn.disabled = false;
                exitBtn.innerHTML = originalText;
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
            exitBtn.disabled = false;
            exitBtn.innerHTML = originalText;
        }
    }

    // تنظیم اولیه
    document.addEventListener('DOMContentLoaded', function() {
        updateExitButton();
    });
</script>
