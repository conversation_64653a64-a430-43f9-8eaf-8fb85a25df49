@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Buildings.DetailsModel
@{
    ViewData["Title"] = "جزئیات ساختمان";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>جزئیات ساختمان</h1>
        <div>
            <a asp-page="Edit" asp-route-id="@Model.Building.BuildingId" class="btn btn-primary">
                <i class="bi bi-pencil"></i> ویرایش
            </a>
            <a asp-page="Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به لیست
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">@Model.Building.Name</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">شناسه:</dt>
                    <dd class="col-sm-9">@Model.Building.BuildingId</dd>
                    
                    <dt class="col-sm-3">نام ساختمان:</dt>
                    <dd class="col-sm-9">
                        <strong>@Model.Building.Name</strong>
                    </dd>
                    
                    <dt class="col-sm-3">توضیحات:</dt>
                    <dd class="col-sm-9">
                        @if (!string.IsNullOrEmpty(Model.Building.Description))
                        {
                            <p class="mb-0">@Model.Building.Description</p>
                        }
                        else
                        {
                            <span class="text-muted">بدون توضیحات</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>

        @if (Model.Employees.Any())
        {
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">کارمندان این ساختمان (@Model.Employees.Count نفر)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>نام و نام خانوادگی</th>
                                    <th>کد ملی</th>
                                    <th>شغل</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var employee in Model.Employees)
                                {
                                    <tr>
                                        <td>@employee.FirstName @employee.LastName</td>
                                        <td>@employee.NationalCode</td>
                                        <td>@employee.Job?.Title</td>
                                        <td>
                                            <a asp-page="/Employees/Details" asp-route-id="@employee.EmployeeId" 
                                               class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-eye"></i> مشاهده
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">عملیات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-page="Edit" asp-route-id="@Model.Building.BuildingId" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> ویرایش ساختمان
                    </a>
                    <a asp-page="/Employees/Create" asp-route-buildingId="@Model.Building.BuildingId" class="btn btn-success">
                        <i class="bi bi-person-plus"></i> افزودن کارمند به این ساختمان
                    </a>
                    <hr>
                    <form asp-page="Delete" asp-route-id="@Model.Building.BuildingId" method="post" 
                          onsubmit="return confirmDelete('آیا مطمئن هستید که می‌خواهید این ساختمان را حذف کنید؟')">
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="bi bi-trash"></i> حذف ساختمان
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">آمار</h5>
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-12">
                        <h3 class="text-primary">@Model.Employees.Count</h3>
                        <p class="mb-0">کارمند در این ساختمان</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
