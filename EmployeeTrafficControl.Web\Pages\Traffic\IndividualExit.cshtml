@page "/traffic/individual-exit"
@model EmployeeTrafficControl.Web.Pages.Traffic.IndividualExitModel
@{
    ViewData["Title"] = "ثبت خروج فردی";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-box-arrow-left"></i> ثبت خروج فردی کارمندان</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">ثبت خروج ساعتی، ماموریت اداری یا خروج نهایی کارمندان</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">جستجو در کارمندان</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="نام، نام خانوادگی یا کد پرسنلی" />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع خروج</label>
                        <select name="ExitType" class="form-select">
                            <option value="">همه انواع</option>
                            <option value="hourly" selected="@(Model.ExitType == "hourly")">خروج ساعتی</option>
                            <option value="mission" selected="@(Model.ExitType == "mission")">ماموریت اداری</option>
                            <option value="final" selected="@(Model.ExitType == "final")">خروج نهایی</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">شماره برگه خروج</label>
                        <input type="text" name="ExitPermitNumber" value="@Model.ExitPermitNumber" class="form-control" 
                               placeholder="شماره برگه (اختیاری)" />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-present">
            <div class="stats-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.PresentEmployees.Count</h3>
                <p>حاضر در ساختمان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-hourly-exit">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.HourlyExitCount</h3>
                <p>خروج ساعتی</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-mission">
            <div class="stats-icon">
                <i class="bi bi-briefcase"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.MissionCount</h3>
                <p>ماموریت اداری</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-final-exit">
            <div class="stats-icon">
                <i class="bi bi-door-open"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.FinalExitCount</h3>
                <p>خروج نهایی</p>
            </div>
        </div>
    </div>
</div>

<!-- لیست کارمندان حاضر -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> کارمندان حاضر در ساختمان
                </h5>
                <span class="badge bg-success">@Model.PresentEmployees.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.PresentEmployees.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>کارمند</th>
                                    <th>کد پرسنلی</th>
                                    <th>ساختمان</th>
                                    <th>شغل</th>
                                    <th>زمان ورود</th>
                                    <th>وضعیت فعلی</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var status in Model.PresentEmployees)
                                {
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>@status.Employee.FirstName @status.Employee.LastName</strong>
                                                @if (!string.IsNullOrEmpty(status.Employee.PhoneNumber))
                                                {
                                                    <br><small class="text-muted">@status.Employee.PhoneNumber</small>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            <code>@status.Employee.PersonnelCode</code>
                                        </td>
                                        <td>
                                            @if (status.Employee.Building != null)
                                            {
                                                <span class="badge bg-info">@status.Employee.Building.Name</span>
                                            }
                                        </td>
                                        <td>
                                            @if (status.Employee.Job != null)
                                            {
                                                <span class="badge bg-secondary">@status.Employee.Job.Title</span>
                                                @if (status.Employee.Job.IsDriver)
                                                {
                                                    <span class="badge bg-warning ms-1">راننده</span>
                                                }
                                            }
                                        </td>
                                        <td>
                                            @if (status.EntryTime.HasValue)
                                            {
                                                <span class="text-success">@status.EntryTime.Value.ToString("HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @status.GetStatusBadgeClass()">
                                                @status.GetStatusDisplayName()
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="showExitModal(@status.EmployeeId, '@status.Employee.FirstName @status.Employee.LastName', 'hourly')">
                                                    <i class="bi bi-clock"></i> خروج ساعتی
                                                </button>
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="showExitModal(@status.EmployeeId, '@status.Employee.FirstName @status.Employee.LastName', 'mission')">
                                                    <i class="bi bi-briefcase"></i> ماموریت
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="showExitModal(@status.EmployeeId, '@status.Employee.FirstName @status.Employee.LastName', 'final')">
                                                    <i class="bi bi-door-open"></i> خروج نهایی
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-person-slash" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">کارمندی در ساختمان حضور ندارد</h5>
                        <p>همه کارمندان خارج از ساختمان هستند یا هنوز ورود ثبت نشده است.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Modal ثبت خروج -->
<div class="modal fade" id="exitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exitModalTitle">ثبت خروج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exitForm">
                    <input type="hidden" id="employeeId" />
                    <input type="hidden" id="exitType" />
                    
                    <div class="mb-3">
                        <label class="form-label">کارمند:</label>
                        <div id="employeeName" class="fw-bold"></div>
                    </div>
                    
                    <div class="mb-3" id="permitNumberGroup">
                        <label for="permitNumber" class="form-label">شماره برگه خروج</label>
                        <input type="text" class="form-control" id="permitNumber" placeholder="شماره برگه (اختیاری)">
                    </div>
                    
                    <div class="mb-3">
                        <label for="exitNotes" class="form-label">توضیحات</label>
                        <textarea class="form-control" id="exitNotes" rows="3" placeholder="توضیحات خروج (اختیاری)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-primary" onclick="registerExit()">ثبت خروج</button>
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-hourly-exit {
        border-right: 4px solid #ffc107;
    }
    .stats-hourly-exit .stats-icon { color: #ffc107; }

    .stats-mission {
        border-right: 4px solid #17a2b8;
    }
    .stats-mission .stats-icon { color: #17a2b8; }

    .stats-final-exit {
        border-right: 4px solid #dc3545;
    }
    .stats-final-exit .stats-icon { color: #dc3545; }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .btn-group .btn {
        margin-left: 2px;
    }
</style>

<script>
    let currentEmployeeId = null;
    let currentExitType = null;

    function showExitModal(employeeId, employeeName, exitType) {
        currentEmployeeId = employeeId;
        currentExitType = exitType;

        document.getElementById('employeeId').value = employeeId;
        document.getElementById('exitType').value = exitType;
        document.getElementById('employeeName').textContent = employeeName;

        // تنظیم عنوان modal بر اساس نوع خروج
        const modalTitle = document.getElementById('exitModalTitle');
        const permitGroup = document.getElementById('permitNumberGroup');

        switch(exitType) {
            case 'hourly':
                modalTitle.textContent = 'ثبت خروج ساعتی';
                permitGroup.style.display = 'block';
                break;
            case 'mission':
                modalTitle.textContent = 'ثبت ماموریت اداری';
                permitGroup.style.display = 'block';
                break;
            case 'final':
                modalTitle.textContent = 'ثبت خروج نهایی';
                permitGroup.style.display = 'none';
                break;
        }

        // پاک کردن فرم
        document.getElementById('permitNumber').value = '';
        document.getElementById('exitNotes').value = '';

        // نمایش modal
        const modal = new bootstrap.Modal(document.getElementById('exitModal'));
        modal.show();
    }

    async function registerExit() {
        if (!currentEmployeeId || !currentExitType) {
            alert('خطا در اطلاعات خروج');
            return;
        }

        const permitNumber = document.getElementById('permitNumber').value;
        const notes = document.getElementById('exitNotes').value;

        if (currentExitType === 'hourly' && !permitNumber.trim()) {
            if (!confirm('آیا مطمئن هستید که می‌خواهید بدون شماره برگه خروج ساعتی ثبت کنید؟')) {
                return;
            }
        }

        try {
            const response = await fetch('/api/traffic/register-exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeIds: [currentEmployeeId],
                    exitType: currentExitType,
                    exitPermitNumber: permitNumber || null,
                    notes: notes || null
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'خروج با موفقیت ثبت شد');

                // بستن modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('exitModal'));
                modal.hide();

                // رفرش صفحه
                location.reload();
            } else {
                alert(result.message || 'خطا در ثبت خروج. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
        }
    }
</script>
