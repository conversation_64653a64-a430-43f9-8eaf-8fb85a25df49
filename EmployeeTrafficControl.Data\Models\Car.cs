﻿using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Models
{
    public class Car
    {
        [Key]
        [Display(Name = "شناسه خودرو")]
        public int CarId { get; set; }

        [Required(ErrorMessage = "شماره پلاک اجباری است.")]
        [StringLength(20, ErrorMessage = "شماره پلاک حداکثر 20 کاراکتر باشد.")]
        [Display(Name = "شماره پلاک")]
        public string PlateNumber { get; set; }

        [StringLength(50, ErrorMessage = "مدل خودرو حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "مدل")]
        public string? Model { get; set; }

        [StringLength(50, ErrorMessage = "رنگ خودرو حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "رنگ")]
        public string? Color { get; set; }

        [StringLength(50, ErrorMessage = "نوع خودرو حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نوع")]
        public string? Type { get; set; }

        [Display(Name = "ظرفیت سرنشین")]
        public int PassengerCapacity { get; set; } = 1;

        [Display(Name = "خودرو پولرسان")]
        public bool IsMoneyTransport { get; set; } = false;

        [StringLength(50, ErrorMessage = "وضعیت حضور حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "وضعیت حضور")]
        public string CurrentStatus { get; set; } = "در پارکینگ";

        [Required(ErrorMessage = "انتخاب ساختمان اجباری است.")]
        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        // Navigation Properties
        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;

        public ICollection<CarTrafficLog> CarTrafficLogs { get; set; } = new List<CarTrafficLog>();
        public ICollection<CarKilometer> CarKilometers { get; set; } = new List<CarKilometer>();
    }
}