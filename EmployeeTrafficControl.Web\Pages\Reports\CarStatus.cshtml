@page "/reports/car-status"
@model EmployeeTrafficControl.Web.Pages.Reports.CarStatusModel
@using EmployeeTrafficControl.Models
@{
    ViewData["Title"] = "وضعیت خودروها";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-car-front-fill"></i> وضعیت خودروها</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">نمایش وضعیت همه خودروها در پارکینگ</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">جستجو</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="شماره پلاک، راننده یا مهمان" />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">وضعیت</label>
                        <select name="StatusFilter" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="@((int)CarStatus.InsideParking)" selected="@(Model.StatusFilter == (int)CarStatus.InsideParking)">در پارکینگ</option>
                            <option value="@((int)CarStatus.OutsideParking)" selected="@(Model.StatusFilter == (int)CarStatus.OutsideParking)">خارج از پارکینگ</option>
                            <option value="@((int)CarStatus.Entering)" selected="@(Model.StatusFilter == (int)CarStatus.Entering)">در حال ورود</option>
                            <option value="@((int)CarStatus.Exiting)" selected="@(Model.StatusFilter == (int)CarStatus.Exiting)">در حال خروج</option>
                            <option value="@((int)CarStatus.Blocked)" selected="@(Model.StatusFilter == (int)CarStatus.Blocked)">مسدود شده</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع خودرو</label>
                        <select name="CarType" class="form-select">
                            <option value="">همه انواع</option>
                            <option value="employee" selected="@(Model.CarType == "employee")">خودرو کارمند</option>
                            <option value="guest" selected="@(Model.CarType == "guest")">خودرو مهمان</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="stats-card stats-inside">
            <div class="stats-icon">
                <i class="bi bi-car-front-fill"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.InsideCount</h3>
                <p>در پارکینگ</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="stats-card stats-outside">
            <div class="stats-icon">
                <i class="bi bi-car-front"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.OutsideCount</h3>
                <p>خارج از پارکینگ</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="stats-card stats-employee">
            <div class="stats-icon">
                <i class="bi bi-person-badge"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeeCarCount</h3>
                <p>خودرو کارمندان</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="stats-card stats-guest">
            <div class="stats-icon">
                <i class="bi bi-person-plus"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.GuestCarCount</h3>
                <p>خودرو مهمانان</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="stats-card stats-blocked">
            <div class="stats-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.BlockedCount</h3>
                <p>مسدود شده</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="stats-card stats-total">
            <div class="stats-icon">
                <i class="bi bi-list-ul"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TotalCount</h3>
                <p>کل خودروها</p>
            </div>
        </div>
    </div>
</div>

<!-- لیست خودروها -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> لیست خودروها
                </h5>
                <span class="badge bg-primary">@Model.Cars.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.Cars.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>شماره پلاک</th>
                                    <th>نوع</th>
                                    <th>راننده/مهمان</th>
                                    <th>ساختمان</th>
                                    <th>وضعیت</th>
                                    <th>زمان ورود</th>
                                    <th>زمان خروج</th>
                                    <th>مدت حضور</th>
                                    <th>توضیحات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var car in Model.Cars)
                                {
                                    var duration = car.GetParkingDuration();
                                    
                                    <tr class="car-row">
                                        <td>
                                            <strong class="text-primary">@car.PlateNumber</strong>
                                        </td>
                                        <td>
                                            <span class="badge @(car.DriverEmployeeId != null ? "bg-info" : "bg-warning")">
                                                @car.GetCarType()
                                            </span>
                                        </td>
                                        <td>
                                            @if (car.DriverEmployee != null)
                                            {
                                                <div>
                                                    <strong>@car.GetDriverOrGuestName()</strong>
                                                    <br><small class="text-muted">@car.DriverEmployee.PersonnelCode</small>
                                                </div>
                                            }
                                            else
                                            {
                                                <span class="@(string.IsNullOrEmpty(car.GuestName) ? "text-muted" : "text-warning")">
                                                    @car.GetDriverOrGuestName()
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @if (car.Building != null)
                                            {
                                                <span class="badge bg-secondary">@car.Building.Name</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @car.GetStatusBadgeClass()">
                                                <i class="@car.GetStatusIcon()"></i>
                                                @car.GetStatusDisplayName()
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-success">@(car.EntryTime?.ToString("HH:mm") ?? "-")</span>
                                            <br><small class="text-muted">@(car.EntryTime?.ToString("yyyy/MM/dd") ?? "-")</small>
                                        </td>
                                        <td>
                                            @if (car.ExitTime.HasValue)
                                            {
                                                <span class="text-danger">@car.ExitTime.Value.ToString("HH:mm")</span>
                                                <br><small class="text-muted">@car.ExitTime.Value.ToString("yyyy/MM/dd")</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (duration.HasValue)
                                            {
                                                var hours = (int)duration.Value.TotalHours;
                                                var minutes = duration.Value.Minutes;
                                                <span class="text-info">@hours:@minutes.ToString("D2")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(car.Notes))
                                            {
                                                <small class="text-muted">@car.Notes</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-car-front" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">خودرویی یافت نشد</h5>
                        <p>با فیلترهای انتخاب شده خودرویی پیدا نشد.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-inside {
        border-right: 4px solid #28a745;
    }
    .stats-inside .stats-icon { color: #28a745; }

    .stats-outside {
        border-right: 4px solid #6c757d;
    }
    .stats-outside .stats-icon { color: #6c757d; }

    .stats-employee {
        border-right: 4px solid #007bff;
    }
    .stats-employee .stats-icon { color: #007bff; }

    .stats-guest {
        border-right: 4px solid #ffc107;
    }
    .stats-guest .stats-icon { color: #ffc107; }

    .stats-blocked {
        border-right: 4px solid #dc3545;
    }
    .stats-blocked .stats-icon { color: #dc3545; }

    .stats-total {
        border-right: 4px solid #17a2b8;
    }
    .stats-total .stats-icon { color: #17a2b8; }

    .car-row:hover {
        background-color: #f8f9fa;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }
</style>
