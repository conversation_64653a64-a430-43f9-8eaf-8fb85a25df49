{"Files": [{"Id": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\app.css", "PackagePath": "staticwebassets\\app.css"}, {"Id": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "PackagePath": "staticwebassets\\bootstrap\\bootstrap.min.css"}, {"Id": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\bootstrap\\bootstrap.min.css.map"}, {"Id": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\favicon.png", "PackagePath": "staticwebassets\\favicon.png"}, {"Id": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.EmployeeTrafficControl.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.EmployeeTrafficControl.Web.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.EmployeeTrafficControl.Web.props", "PackagePath": "build\\EmployeeTrafficControl.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.EmployeeTrafficControl.Web.props", "PackagePath": "buildMultiTargeting\\EmployeeTrafficControl.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.EmployeeTrafficControl.Web.props", "PackagePath": "buildTransitive\\EmployeeTrafficControl.Web.props"}], "ElementsToRemove": []}