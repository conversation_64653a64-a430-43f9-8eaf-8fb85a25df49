@page
@model EmployeeTrafficControl.Web.Pages.Jobs.CreateModel
@{
    ViewData["Title"] = "افزودن شغل جدید";
}

<div class="page-header">
    <h1>افزودن شغل جدید</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="form-section">
                        <div class="mb-3">
                            <label asp-for="Job.Title" class="form-label">عنوان شغل <span class="text-danger">*</span></label>
                            <input asp-for="Job.Title" class="form-control" placeholder="مثال: مهندس نرم‌افزار" />
                            <span asp-validation-for="Job.Title" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="Job.IsDriver" class="form-check-input" type="checkbox" />
                                <label asp-for="Job.IsDriver" class="form-check-label">
                                    این شغل شامل رانندگی می‌شود
                                </label>
                            </div>
                            <span asp-validation-for="Job.IsDriver" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره
                        </button>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        عنوان شغل باید منحصر به فرد باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        در صورت انتخاب گزینه راننده، این شغل قابلیت رانندگی خواهد داشت
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
