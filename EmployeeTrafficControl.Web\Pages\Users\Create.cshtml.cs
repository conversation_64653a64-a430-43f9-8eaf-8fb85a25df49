using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    public class CreateModel : PageModel
    {
        private readonly UserService _userService;
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;

        public CreateModel(UserService userService, EmployeeService employeeService, BuildingService buildingService)
        {
            _userService = userService;
            _employeeService = employeeService;
            _buildingService = buildingService;
        }

        [BindProperty]
        public User User { get; set; } = default!;

        [BindProperty]
        public string Password { get; set; } = default!;

        public SelectList Employees { get; set; } = default!;
        public SelectList Buildings { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadSelectLists();
            User = new User();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (string.IsNullOrEmpty(Password) || Password.Length < 6)
            {
                ModelState.AddModelError("Password", "رمز عبور باید حداقل 6 کاراکتر باشد.");
            }

            if (!ModelState.IsValid)
            {
                await LoadSelectLists();
                return Page();
            }

            // Check if username already exists
            bool usernameExists = await _userService.UsernameExistsAsync(User.Username, 0);
            if (usernameExists)
            {
                ModelState.AddModelError("User.Username", "نام کاربری وارد شده قبلاً ثبت شده است.");
                await LoadSelectLists();
                return Page();
            }

            try
            {
                // Hash the password (simple hash for demo - use proper hashing in production)
                User.PasswordHash = BCrypt.Net.BCrypt.HashPassword(Password);
                
                await _userService.AddUserAsync(User);
                TempData["SuccessMessage"] = "کاربر جدید با موفقیت اضافه شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var employees = await _employeeService.GetAllEmployeesAsync();
            var buildings = await _buildingService.GetAllBuildingsAsync();

            Employees = new SelectList(employees.Select(e => new { 
                Value = e.EmployeeId, 
                Text = $"{e.FirstName} {e.LastName} ({e.PersonnelCode})" 
            }), "Value", "Text");
            
            Buildings = new SelectList(buildings, "BuildingId", "Name");
        }
    }
}
