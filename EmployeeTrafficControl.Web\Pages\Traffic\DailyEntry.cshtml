@page "/traffic/daily-entry"
@model EmployeeTrafficControl.Web.Pages.Traffic.DailyEntryModel
@{
    ViewData["Title"] = "ثبت ورود روزانه";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-box-arrow-in-right"></i> ثبت ورود روزانه کارمندان</h1>
        <div>
            <a asp-page="/Dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">ثبت ورود اولیه کارمندان در شروع روز کاری</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">جستجو در کارمندان</label>
                        <input type="text" name="search" value="@Model.SearchTerm" class="form-control" 
                               placeholder="نام، نام خانوادگی یا کد پرسنلی" />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="buildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">شغل</label>
                        <select name="jobId" class="form-select" asp-items="Model.Jobs">
                            <option value="">همه مشاغل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-total">
            <div class="stats-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TotalEmployees</h3>
                <p>کل کارمندان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-present">
            <div class="stats-icon">
                <i class="bi bi-person-check"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.PresentCount</h3>
                <p>ورود ثبت شده</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-absent">
            <div class="stats-icon">
                <i class="bi bi-person-x"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.AbsentCount</h3>
                <p>ورود ثبت نشده</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-late">
            <div class="stats-icon">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.LateCount</h3>
                <p>تأخیر داشته‌اند</p>
            </div>
        </div>
    </div>
</div>

<!-- عملیات گروهی -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> عملیات گروهی
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-success w-100" onclick="selectAllEmployees()">
                            <i class="bi bi-check-all"></i> انتخاب همه کارمندان
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary w-100" onclick="registerSelectedEntries()" id="registerBtn" disabled>
                            <i class="bi bi-box-arrow-in-right"></i> ثبت ورود انتخاب شده‌ها
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- لیست کارمندان -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> لیست کارمندان
                </h5>
                <span class="badge bg-info">@Model.Employees.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.Employees.Any())
                {
                    <form id="entryForm">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll" class="form-check-input" onchange="toggleAllSelection()">
                                        </th>
                                        <th>کارمند</th>
                                        <th>کد پرسنلی</th>
                                        <th>ساختمان</th>
                                        <th>شغل</th>
                                        <th>وضعیت</th>
                                        <th>زمان ورود</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var employee in Model.Employees)
                                    {
                                        var attendance = Model.TodayAttendances.FirstOrDefault(a => a.EmployeeId == employee.EmployeeId);
                                        var status = Model.EmployeeStatuses.FirstOrDefault(s => s.EmployeeId == employee.EmployeeId);
                                        
                                        <tr class="employee-row" data-employee-id="@employee.EmployeeId">
                                            <td>
                                                @if (attendance == null || !attendance.CheckInTime.HasValue)
                                                {
                                                    <input type="checkbox" class="form-check-input employee-checkbox" 
                                                           value="@employee.EmployeeId" onchange="updateRegisterButton()">
                                                }
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <strong>@employee.FirstName @employee.LastName</strong>
                                                        @if (!string.IsNullOrEmpty(employee.PhoneNumber))
                                                        {
                                                            <br><small class="text-muted">@employee.PhoneNumber</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code>@employee.PersonnelCode</code>
                                            </td>
                                            <td>
                                                @if (employee.Building != null)
                                                {
                                                    <span class="badge bg-info">@employee.Building.Name</span>
                                                }
                                            </td>
                                            <td>
                                                @if (employee.Job != null)
                                                {
                                                    <span class="badge bg-secondary">@employee.Job.Title</span>
                                                    @if (employee.Job.IsDriver)
                                                    {
                                                        <span class="badge bg-warning ms-1">راننده</span>
                                                    }
                                                }
                                            </td>
                                            <td>
                                                @if (attendance != null && attendance.CheckInTime.HasValue)
                                                {
                                                    <span class="badge bg-success">ورود ثبت شده</span>
                                                    @if (attendance.LateMinutes > 0)
                                                    {
                                                        <br><span class="badge bg-warning mt-1">@attendance.LateMinutes دقیقه تأخیر</span>
                                                    }
                                                }
                                                else if (status != null && status.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding)
                                                {
                                                    <span class="badge bg-info">حضور در ساختمان</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">ورود ثبت نشده</span>
                                                }
                                            </td>
                                            <td>
                                                @if (attendance?.CheckInTime.HasValue == true)
                                                {
                                                    <span class="text-success">@attendance.CheckInTime.Value.ToString("HH:mm")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (attendance == null || !attendance.CheckInTime.HasValue)
                                                {
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="registerSingleEntry(@employee.EmployeeId)">
                                                        <i class="bi bi-check"></i> ثبت ورود
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="text-success">
                                                        <i class="bi bi-check-circle"></i> ثبت شده
                                                    </span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </form>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-person-slash" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">کارمندی یافت نشد</h5>
                        <p>با فیلترهای انتخاب شده کارمندی پیدا نشد.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-total {
        border-right: 4px solid #6c757d;
    }
    .stats-total .stats-icon { color: #6c757d; }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-absent {
        border-right: 4px solid #dc3545;
    }
    .stats-absent .stats-icon { color: #dc3545; }

    .stats-late {
        border-right: 4px solid #ffc107;
    }
    .stats-late .stats-icon { color: #ffc107; }

    .employee-row:hover {
        background-color: #f8f9fa;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }
</style>

<script>
    function toggleAllSelection() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.employee-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateRegisterButton();
    }

    function selectAllEmployees() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        document.getElementById('selectAll').checked = true;
        updateRegisterButton();
    }

    function updateRegisterButton() {
        const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');
        const registerBtn = document.getElementById('registerBtn');

        if (checkedBoxes.length > 0) {
            registerBtn.disabled = false;
            registerBtn.innerHTML = `<i class="bi bi-box-arrow-in-right"></i> ثبت ورود ${checkedBoxes.length} نفر`;
        } else {
            registerBtn.disabled = true;
            registerBtn.innerHTML = '<i class="bi bi-box-arrow-in-right"></i> ثبت ورود انتخاب شده‌ها';
        }
    }

    async function registerSingleEntry(employeeId) {
        if (!confirm('آیا مطمئن هستید که می‌خواهید ورود این کارمند را ثبت کنید؟')) {
            return;
        }

        try {
            const response = await fetch('/api/traffic/register-entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                },
                body: JSON.stringify({ employeeIds: [employeeId] })
            });

            if (response.ok) {
                location.reload();
            } else {
                alert('خطا در ثبت ورود. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            alert('خطا در ارتباط با سرور.');
        }
    }

    async function registerSelectedEntries() {
        const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');
        const employeeIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));

        if (employeeIds.length === 0) {
            alert('لطفاً حداقل یک کارمند انتخاب کنید.');
            return;
        }

        if (!confirm(`آیا مطمئن هستید که می‌خواهید ورود ${employeeIds.length} کارمند را ثبت کنید؟`)) {
            return;
        }

        const registerBtn = document.getElementById('registerBtn');
        const originalText = registerBtn.innerHTML;
        registerBtn.disabled = true;
        registerBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> در حال ثبت...';

        try {
            const response = await fetch('/api/traffic/register-entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                },
                body: JSON.stringify({ employeeIds: employeeIds })
            });

            if (response.ok) {
                location.reload();
            } else {
                alert('خطا در ثبت ورود. لطفاً دوباره تلاش کنید.');
                registerBtn.disabled = false;
                registerBtn.innerHTML = originalText;
            }
        } catch (error) {
            alert('خطا در ارتباط با سرور.');
            registerBtn.disabled = false;
            registerBtn.innerHTML = originalText;
        }
    }

    // تنظیم اولیه
    document.addEventListener('DOMContentLoaded', function() {
        updateRegisterButton();
    });
</script>
