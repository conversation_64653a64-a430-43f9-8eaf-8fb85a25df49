﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class SeedEmployeesAndCars : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert Employees
            migrationBuilder.Sql(@"
                INSERT INTO Employees (NationalCode, FirstName, LastName, PersonnelCode, PhoneNumber, BuildingId, JobId, IsActive)
                VALUES
                ('1234567890', N'احمد', N'محمدی', 'EMP001', '09121234567', 1, 1, 1),
                ('2345678901', N'فاطمه', N'احمدی', 'EMP002', '09122345678', 1, 2, 1),
                ('3456789012', N'علی', N'رضایی', 'EMP003', '09123456789', 1, 3, 1),
                ('4567890123', N'مریم', N'حسینی', 'EMP004', '09124567890', 2, 3, 1),
                ('5678901234', N'حسن', <PERSON>'کریمی', '<PERSON><PERSON>005', '09125678901', 2, 4, 1),
                ('6789012345', N'زهرا', N'موسوی', 'EMP006', '09126789012', 3, 7, 1),
                ('7890123456', N'محمد', N'صادقی', 'EMP007', '09127890123', 1, 5, 1),
                ('8901234567', N'سارا', N'نوری', 'EMP008', '09128901234', 2, 8, 1),
                ('9012345678', N'رضا', N'جعفری', 'EMP009', '09129012345', 3, 9, 1),
                ('0123456789', N'لیلا', N'زارعی', 'EMP010', '09120123456', 4, 10, 1)
            ");

            // Insert Cars
            migrationBuilder.Sql(@"
                INSERT INTO Cars (PlateNumber, Model, Color, Type, PassengerCapacity)
                VALUES
                (N'12ط345-23', N'پژو پارس', N'سفید', N'سدان', 5),
                (N'34ج567-45', N'سمند', N'نقره‌ای', N'سدان', 5),
                (N'56د789-67', N'دنا', N'مشکی', N'سدان', 5),
                (N'78ب901-89', N'تیبا', N'آبی', N'هاچ‌بک', 5),
                (N'90ا123-12', N'ساینا', N'قرمز', N'سدان', 5),
                (N'23ه456-34', N'رانا', N'سفید', N'سدان', 5),
                (N'45و678-56', N'کوییک', N'طوسی', N'هاچ‌بک', 5),
                (N'67ی890-78', N'شاهین', N'مشکی', N'سدان', 5)
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove seed data
            migrationBuilder.Sql("DELETE FROM Cars WHERE PlateNumber IN (N'12ط345-23', N'34ج567-45', N'56د789-67', N'78ب901-89', N'90ا123-12', N'23ه456-34', N'45و678-56', N'67ی890-78')");
            migrationBuilder.Sql("DELETE FROM Employees WHERE PersonnelCode IN ('EMP001', 'EMP002', 'EMP003', 'EMP004', 'EMP005', 'EMP006', 'EMP007', 'EMP008', 'EMP009', 'EMP010')");
        }
    }
}
