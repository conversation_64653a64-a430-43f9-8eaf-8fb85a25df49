using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    public class CreateModel : PageModel
    {
        private readonly CarService _carService;

        public CreateModel(CarService carService)
        {
            _carService = carService;
        }

        [BindProperty]
        public Car Car { get; set; } = default!;

        public IActionResult OnGet()
        {
            Car = new Car { PassengerCapacity = 5 }; // Default capacity
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Check if car plate number already exists
            bool carExists = await _carService.CarExistsAsync(Car.PlateNumber, null);
            if (carExists)
            {
                ModelState.AddModelError("Car.PlateNumber", "شماره پلاک وارد شده قبلاً ثبت شده است.");
                return Page();
            }

            try
            {
                await _carService.AddCarAsync(Car);
                TempData["SuccessMessage"] = "خودرو جدید با موفقیت اضافه شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                return Page();
            }
        }
    }
}
