using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Jobs
{
    public class IndexModel : PageModel
    {
        private readonly JobService _jobService;

        public IndexModel(JobService jobService)
        {
            _jobService = jobService;
        }

        public IList<Job> Jobs { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Jobs = await _jobService.GetAllJobsAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var job = await _jobService.GetJobByIdAsync(id);
            if (job == null)
            {
                TempData["ErrorMessage"] = "شغل مورد نظر یافت نشد.";
                return RedirectToPage();
            }

            try
            {
                bool deleteResult = await _jobService.DeleteJobAsync(id);
                if (deleteResult)
                {
                    TempData["SuccessMessage"] = "شغل با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این شغل وجود ندارد زیرا کارمندانی به آن اختصاص یافته‌اند.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف شغل: " + ex.Message;
            }

            return RedirectToPage();
        }
    }
}
