using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    public class EditModel : PageModel
    {
        private readonly UserService _userService;
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;

        public EditModel(UserService userService, EmployeeService employeeService, BuildingService buildingService)
        {
            _userService = userService;
            _employeeService = employeeService;
            _buildingService = buildingService;
        }

        [BindProperty]
        public new User User { get; set; } = default!;

        [BindProperty]
        public string? Password { get; set; }

        public SelectList Employees { get; set; } = default!;
        public SelectList Buildings { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            User = await _userService.GetUserByIdAsync(id);

            if (User == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            await LoadSelectLists();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!string.IsNullOrEmpty(Password) && Password.Length < 6)
            {
                ModelState.AddModelError("Password", "رمز عبور باید حداقل 6 کاراکتر باشد.");
            }

            if (!ModelState.IsValid)
            {
                await LoadSelectLists();
                return Page();
            }

            // Check if username already exists (excluding current user)
            bool usernameExists = await _userService.UserExistsAsync(User.Username, User.UserId);
            if (usernameExists)
            {
                ModelState.AddModelError("User.Username", "نام کاربری وارد شده قبلاً ثبت شده است.");
                await LoadSelectLists();
                return Page();
            }

            try
            {
                // Update password only if provided
                if (!string.IsNullOrEmpty(Password))
                {
                    User.PasswordHash = BCrypt.Net.BCrypt.HashPassword(Password);
                }

                await _userService.UpdateUserAsync(User);
                TempData["SuccessMessage"] = "اطلاعات کاربر با موفقیت به‌روزرسانی شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var employees = await _employeeService.GetAllEmployeesAsync();
            var buildings = await _buildingService.GetAllBuildingsAsync();

            Employees = new SelectList(employees.Select(e => new { 
                Value = e.EmployeeId, 
                Text = $"{e.FirstName} {e.LastName} ({e.PersonnelCode})" 
            }), "Value", "Text");
            
            Buildings = new SelectList(buildings, "BuildingId", "Name");
        }
    }
}
