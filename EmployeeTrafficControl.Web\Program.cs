using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Web.Extensions;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorPages();
builder.Services.AddControllers();

// اضافه کردن Session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromHours(8);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.Name = "EmployeeTrafficControl.Session";
});

// Configure the database connection string
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ??
                       throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));

// Register custom services here
builder.Services.AddScoped<BuildingService>();
builder.Services.AddScoped<JobService>();
builder.Services.AddScoped<EmployeeService>();
builder.Services.AddScoped<CarService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<SystemSettingsService>();
builder.Services.AddScoped<AuthenticationService>();
builder.Services.AddScoped<EmployeeStatusService>();
builder.Services.AddScoped<DailyAttendanceService>();

// اضافه کردن سرویس راه‌اندازی دیتابیس
builder.Services.AddDatabaseInitializer();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseSession();
app.UseAuthorization();

app.MapRazorPages();
app.MapControllers();

// تنظیم صفحه پیش‌فرض به Login
app.MapGet("/", () => Results.Redirect("/login"));

// راه‌اندازی اولیه دیتابیس
await app.InitializeDatabaseAsync();

app.Run();
