using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    public class DetailsModel : PageModel
    {
        private readonly CarService _carService;

        public DetailsModel(CarService carService)
        {
            _carService = carService;
        }

        public Car Car { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Car = await _carService.GetCarByIdAsync(id);

            if (Car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var car = await _carService.GetCarByIdAsync(id);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _carService.DeleteCarAsync(id);
                TempData["SuccessMessage"] = "خودرو با موفقیت حذف شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف خودرو: " + ex.Message;
                return RedirectToPage("./Details", new { id });
            }
        }
    }
}
