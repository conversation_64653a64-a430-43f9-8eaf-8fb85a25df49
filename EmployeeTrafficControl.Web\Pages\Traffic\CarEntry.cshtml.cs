using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class CarEntryModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;

        public CarEntryModel(
            ApplicationDbContext context,
            AuthenticationService authService)
        {
            _context = context;
            _authService = authService;
        }

        public List<Employee> Drivers { get; set; } = new();
        public List<CarTrafficLog> CarsInside { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? CarType { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? Status { get; set; }

        public int TotalCarsInside { get; set; }
        public int EmployeeCarsInside { get; set; }
        public int GuestCarsInside { get; set; }
        public int TodayEntries { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            if (!CanRegisterTraffic(session.User.Role))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(session.User);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(User currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری لیست رانندگان (کارمندانی که گواهینامه دارند)
            var driversQuery = _context.Employees
                                     .Include(e => e.Building)
                                     .Include(e => e.Job)
                                     .Where(e => e.IsActive && e.HasDrivingLicense);

            if (accessibleBuildingIds != null)
            {
                driversQuery = driversQuery.Where(e => accessibleBuildingIds.Contains(e.BuildingId));
            }

            Drivers = await driversQuery.OrderBy(e => e.FirstName)
                                       .ThenBy(e => e.LastName)
                                       .ToListAsync();

            // بارگذاری خودروهای داخل پارکینگ
            var carsQuery = _context.CarTrafficLogs
                                   .Include(c => c.DriverEmployee)
                                   .Include(c => c.Building)
                                   .Where(c => c.CurrentStatus == "Inside");

            if (accessibleBuildingIds != null)
            {
                carsQuery = carsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            // اعمال فیلترها
            if (BuildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            if (!string.IsNullOrEmpty(CarType))
            {
                if (CarType == "employee")
                {
                    carsQuery = carsQuery.Where(c => c.DriverEmployeeId.HasValue);
                }
                else if (CarType == "guest")
                {
                    carsQuery = carsQuery.Where(c => !c.DriverEmployeeId.HasValue);
                }
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                carsQuery = carsQuery.Where(c =>
                    c.PlateNumber.ToLower().Contains(searchLower) ||
                    (c.DriverEmployee != null && 
                     (c.DriverEmployee.FirstName.ToLower().Contains(searchLower) ||
                      c.DriverEmployee.LastName.ToLower().Contains(searchLower) ||
                      c.DriverEmployee.PersonnelCode.ToLower().Contains(searchLower))) ||
                    (c.GuestName != null && c.GuestName.ToLower().Contains(searchLower)));
            }

            CarsInside = await carsQuery.OrderBy(c => c.EntryTime).ToListAsync();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;

            // آمار خودروهای داخل پارکینگ
            var carsInsideQuery = _context.CarTrafficLogs
                                         .Where(c => c.CurrentStatus == "Inside");

            if (accessibleBuildingIds != null)
            {
                carsInsideQuery = carsInsideQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                carsInsideQuery = carsInsideQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            var carsInsideList = await carsInsideQuery.ToListAsync();

            TotalCarsInside = carsInsideList.Count;
            EmployeeCarsInside = carsInsideList.Count(c => c.DriverEmployeeId.HasValue);
            GuestCarsInside = carsInsideList.Count(c => !c.DriverEmployeeId.HasValue);

            // آمار ورودی امروز
            var todayEntriesQuery = _context.CarTrafficLogs
                                           .Where(c => c.EntryTime.Date == today);

            if (accessibleBuildingIds != null)
            {
                todayEntriesQuery = todayEntriesQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                todayEntriesQuery = todayEntriesQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            TodayEntries = await todayEntriesQuery.CountAsync();
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(string userRole)
        {
            return userRole == "Admin" || userRole == "Manager" || userRole == "Guard";
        }
    }
}
