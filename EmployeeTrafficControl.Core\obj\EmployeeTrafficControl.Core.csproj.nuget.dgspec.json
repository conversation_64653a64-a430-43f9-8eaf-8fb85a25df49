{"format": 1, "restore": {"G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Core\\EmployeeTrafficControl.Core.csproj": {}}, "projects": {"G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Core\\EmployeeTrafficControl.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Core\\EmployeeTrafficControl.Core.csproj", "projectName": "EmployeeTrafficControl.Core", "projectPath": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Core\\EmployeeTrafficControl.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Data\\EmployeeTrafficControl.Data.csproj": {"projectPath": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Data\\EmployeeTrafficControl.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.106/PortableRuntimeIdentifierGraph.json"}}}, "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Data\\EmployeeTrafficControl.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Data\\EmployeeTrafficControl.Data.csproj", "projectName": "EmployeeTrafficControl.Data", "projectPath": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Data\\EmployeeTrafficControl.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.106/PortableRuntimeIdentifierGraph.json"}}}}}