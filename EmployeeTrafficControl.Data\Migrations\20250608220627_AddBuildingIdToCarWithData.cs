﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddBuildingIdToCarWithData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // اضافه کردن ستون BuildingId به جدول Cars
            migrationBuilder.AddColumn<int>(
                name: "BuildingId",
                table: "Cars",
                type: "int",
                nullable: false,
                defaultValue: 1); // مقدار پیش‌فرض 1 (ساختمان اول)

            // تنظیم BuildingId برای همه خودروهای موجود
            migrationBuilder.Sql("UPDATE Cars SET BuildingId = 1 WHERE BuildingId = 0");

            // ایجاد Index
            migrationBuilder.CreateIndex(
                name: "IX_Cars_BuildingId",
                table: "Cars",
                column: "BuildingId");

            // اضافه کردن Foreign Key
            migrationBuilder.AddForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars",
                column: "BuildingId",
                principalTable: "Buildings",
                principalColumn: "BuildingId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // حذف Foreign Key
            migrationBuilder.DropForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars");

            // حذف Index
            migrationBuilder.DropIndex(
                name: "IX_Cars_BuildingId",
                table: "Cars");

            // حذف ستون BuildingId
            migrationBuilder.DropColumn(
                name: "BuildingId",
                table: "Cars");
        }
    }
}
