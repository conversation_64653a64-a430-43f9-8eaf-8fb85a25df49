using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Jobs
{
    public class CreateModel : PageModel
    {
        private readonly JobService _jobService;

        public CreateModel(JobService jobService)
        {
            _jobService = jobService;
        }

        [BindProperty]
        public Job Job { get; set; } = default!;

        public IActionResult OnGet()
        {
            Job = new Job();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Check if job title already exists
            bool jobExists = await _jobService.JobExistsAsync(Job.Title, null);
            if (jobExists)
            {
                ModelState.AddModelError("Job.Title", "عنوان شغل وارد شده قبلاً ثبت شده است.");
                return Page();
            }

            try
            {
                await _jobService.AddJobAsync(Job);
                TempData["SuccessMessage"] = "شغل جدید با موفقیت اضافه شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                return Page();
            }
        }
    }
}
