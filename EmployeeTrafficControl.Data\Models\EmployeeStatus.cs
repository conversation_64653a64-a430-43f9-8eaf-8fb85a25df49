using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Models
{
    public enum EmployeeCurrentStatus
    {
        [Display(Name = "خارج از اداره")]
        OutOfOffice = 0,
        
        [Display(Name = "حضور در ساختمان")]
        PresentInBuilding = 1,
        
        [Display(Name = "خروج ساعتی")]
        HourlyExit = 2,
        
        [Display(Name = "ماموریت اداری")]
        OfficialMission = 3,
        
        [Display(Name = "مرخصی")]
        OnLeave = 4
    }

    public class EmployeeStatus
    {
        [Key]
        public int StatusId { get; set; }

        [Display(Name = "کارمند")]
        [Required]
        public int EmployeeId { get; set; }

        [Display(Name = "وضعیت فعلی")]
        [Required]
        public EmployeeCurrentStatus CurrentStatus { get; set; } = EmployeeCurrentStatus.OutOfOffice;

        [Display(Name = "تاریخ")]
        [Required]
        public DateTime Date { get; set; } = DateTime.Today;

        [Display(Name = "زمان ورود")]
        public DateTime? EntryTime { get; set; }

        [Display(Name = "زمان خروج نهایی")]
        public DateTime? FinalExitTime { get; set; }

        [Display(Name = "زمان آخرین به‌روزرسانی")]
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [Display(Name = "کاربر به‌روزرسانی‌کننده")]
        public int? UpdatedByUserId { get; set; }

        [Display(Name = "توضیحات")]
        [MaxLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "در خودرو")]
        public bool IsInVehicle { get; set; } = false;

        [Display(Name = "شناسه خودرو")]
        public int? CurrentVehicleId { get; set; }

        [Display(Name = "شماره برگه خروج")]
        [MaxLength(50)]
        public string? ExitPermitNumber { get; set; }

        // Navigation properties
        [Display(Name = "کارمند")]
        public Employee Employee { get; set; } = default!;

        [Display(Name = "کاربر به‌روزرسانی‌کننده")]
        public User? UpdatedByUser { get; set; }

        [Display(Name = "خودرو فعلی")]
        public Car? CurrentVehicle { get; set; }

        // Helper methods
        public bool IsPresent()
        {
            return CurrentStatus == EmployeeCurrentStatus.PresentInBuilding ||
                   CurrentStatus == EmployeeCurrentStatus.HourlyExit ||
                   CurrentStatus == EmployeeCurrentStatus.OfficialMission;
        }

        public bool CanExitBuilding()
        {
            return CurrentStatus == EmployeeCurrentStatus.PresentInBuilding;
        }

        public bool CanEnterBuilding()
        {
            return CurrentStatus == EmployeeCurrentStatus.OutOfOffice ||
                   CurrentStatus == EmployeeCurrentStatus.HourlyExit ||
                   CurrentStatus == EmployeeCurrentStatus.OfficialMission;
        }

        public string GetStatusDisplayName()
        {
            return CurrentStatus switch
            {
                EmployeeCurrentStatus.OutOfOffice => "خارج از اداره",
                EmployeeCurrentStatus.PresentInBuilding => "حضور در ساختمان",
                EmployeeCurrentStatus.HourlyExit => "خروج ساعتی",
                EmployeeCurrentStatus.OfficialMission => "ماموریت اداری",
                EmployeeCurrentStatus.OnLeave => "مرخصی",
                _ => "نامشخص"
            };
        }

        public string GetStatusBadgeClass()
        {
            return CurrentStatus switch
            {
                EmployeeCurrentStatus.PresentInBuilding => "bg-success",
                EmployeeCurrentStatus.HourlyExit => "bg-warning",
                EmployeeCurrentStatus.OfficialMission => "bg-info",
                EmployeeCurrentStatus.OnLeave => "bg-secondary",
                EmployeeCurrentStatus.OutOfOffice => "bg-danger",
                _ => "bg-dark"
            };
        }
    }
}
