using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Extensions
{
    public static class DatabaseExtensions
    {
        /// <summary>
        /// راه‌اندازی اولیه دیتابیس
        /// </summary>
        /// <param name="app">WebApplication instance</param>
        /// <returns>Task</returns>
        public static async Task InitializeDatabaseAsync(this WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<DatabaseInitializer>>();

            try
            {
                logger.LogInformation("شروع راه‌اندازی دیتابیس...");
                
                var initializer = services.GetRequiredService<DatabaseInitializer>();
                await initializer.InitializeAsync();
                
                logger.LogInformation("راه‌اندازی دیتابیس با موفقیت تکمیل شد.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "خطا در راه‌اندازی دیتابیس: {Message}", ex.Message);
                
                // در محیط Development، خطا را نمایش بده
                if (app.Environment.IsDevelopment())
                {
                    throw;
                }
                
                // در محیط Production، فقط لاگ کن
                logger.LogCritical("سیستم نمی‌تواند بدون دیتابیس کار کند. لطفاً مشکل را بررسی کنید.");
            }
        }

        /// <summary>
        /// ثبت سرویس‌های مربوط به راه‌اندازی دیتابیس
        /// </summary>
        /// <param name="services">IServiceCollection</param>
        /// <returns>IServiceCollection</returns>
        public static IServiceCollection AddDatabaseInitializer(this IServiceCollection services)
        {
            services.AddScoped<DatabaseInitializer>();
            return services;
        }
    }
}
