using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCarEnhancements : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // اضافه کردن فیلد پولرسان به جدول Cars
            migrationBuilder.AddColumn<bool>(
                name: "IsMoneyTransport",
                table: "Cars",
                type: "bit",
                nullable: false,
                defaultValue: false);

            // اضافه کردن فیلد نوع خروج به جدول CarTrafficLogs
            migrationBuilder.AddColumn<string>(
                name: "ExitType",
                table: "CarTrafficLogs",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            // ایجاد جدول CarKilometers
            migrationBuilder.CreateTable(
                name: "CarKilometers",
                columns: table => new
                {
                    CarKilometerId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CarId = table.Column<int>(type: "int", nullable: false),
                    Date = table.Column<DateTime>(type: "date", nullable: false),
                    StartKilometer = table.Column<int>(type: "int", nullable: false),
                    EndKilometer = table.Column<int>(type: "int", nullable: true),
                    CreatedByUserId = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CarKilometers", x => x.CarKilometerId);
                    table.ForeignKey(
                        name: "FK_CarKilometers_Cars_CarId",
                        column: x => x.CarId,
                        principalTable: "Cars",
                        principalColumn: "CarId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CarKilometers_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CarKilometers_CarId",
                table: "CarKilometers",
                column: "CarId");

            migrationBuilder.CreateIndex(
                name: "IX_CarKilometers_CreatedByUserId",
                table: "CarKilometers",
                column: "CreatedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CarKilometers");

            migrationBuilder.DropColumn(
                name: "IsMoneyTransport",
                table: "Cars");

            migrationBuilder.DropColumn(
                name: "ExitType",
                table: "CarTrafficLogs");
        }
    }
}
