using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Models
{
    public class CarKilometer
    {
        [Key]
        [Display(Name = "شناسه کیلومتر")]
        public int CarKilometerId { get; set; }

        [Display(Name = "خودرو")]
        public int CarId { get; set; }

        [Display(Name = "تاریخ")]
        [Column(TypeName = "date")]
        public DateTime Date { get; set; } = DateTime.Today;

        [Display(Name = "کیلومتر ابتدای روز")]
        [Range(0, 9999999, ErrorMessage = "کیلومتر باید بین 0 تا 9999999 باشد.")]
        public int StartKilometer { get; set; }

        [Display(Name = "کیلومتر انتهای روز")]
        [Range(0, 9999999, ErrorMessage = "کیلومتر باید بین 0 تا 9999999 باشد.")]
        public int? EndKilometer { get; set; }

        [Display(Name = "کاربر ثبت‌کننده")]
        public int CreatedByUserId { get; set; }

        [Display(Name = "زمان ثبت")]
        [Column(TypeName = "datetime2")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [StringLength(500, ErrorMessage = "توضیحات حداکثر 500 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Notes { get; set; }

        // Navigation Properties
        [Display(Name = "خودرو")]
        public Car Car { get; set; } = default!;

        [Display(Name = "کاربر ثبت‌کننده")]
        public User CreatedByUser { get; set; } = default!;

        // Helper Properties
        [NotMapped]
        [Display(Name = "مسافت طی شده")]
        public int? DistanceTraveled => EndKilometer.HasValue ? EndKilometer.Value - StartKilometer : null;

        // Helper Methods
        public bool IsCompleted => EndKilometer.HasValue;

        public string GetStatusDisplayName()
        {
            return IsCompleted ? "تکمیل شده" : "در حال انجام";
        }

        public string GetStatusBadgeClass()
        {
            return IsCompleted ? "bg-success" : "bg-warning";
        }
    }
}
