@page "/admin/system-settings"
@model EmployeeTrafficControl.Web.Pages.Admin.SystemSettingsModel
@{
    ViewData["Title"] = "تنظیمات سیستم";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-gear"></i> تنظیمات سیستم</h1>
        <div>
            <a asp-page="/Dashboard/Admin" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock"></i> تنظیمات ساعات کاری
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.WorkStartTime" class="form-label">ساعت شروع کار <span class="text-danger">*</span></label>
                                <input asp-for="Settings.WorkStartTime" type="time" class="form-control" />
                                <span asp-validation-for="Settings.WorkStartTime" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.WorkEndTime" class="form-label">ساعت پایان کار <span class="text-danger">*</span></label>
                                <input asp-for="Settings.WorkEndTime" type="time" class="form-control" />
                                <span asp-validation-for="Settings.WorkEndTime" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.MaxLateMinutes" class="form-label">حداکثر تأخیر مجاز (دقیقه)</label>
                                <input asp-for="Settings.MaxLateMinutes" type="number" min="0" max="120" class="form-control" />
                                <span asp-validation-for="Settings.MaxLateMinutes" class="text-danger"></span>
                                <div class="form-text">تأخیر بیش از این مقدار به عنوان تأخیر محسوب می‌شود</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.MaxEarlyLeaveMinutes" class="form-label">حداکثر زودتر رفتن مجاز (دقیقه)</label>
                                <input asp-for="Settings.MaxEarlyLeaveMinutes" type="number" min="0" max="120" class="form-control" />
                                <span asp-validation-for="Settings.MaxEarlyLeaveMinutes" class="text-danger"></span>
                                <div class="form-text">خروج زودتر از این مقدار به عنوان زودتر رفتن محسوب می‌شود</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Settings.WorkingDays" class="form-label">روزهای کاری <span class="text-danger">*</span></label>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-check-group">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="saturday" value="Saturday" />
                                        <label class="form-check-label" for="saturday">شنبه</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="sunday" value="Sunday" />
                                        <label class="form-check-label" for="sunday">یکشنبه</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="monday" value="Monday" />
                                        <label class="form-check-label" for="monday">دوشنبه</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="tuesday" value="Tuesday" />
                                        <label class="form-check-label" for="tuesday">سه‌شنبه</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="wednesday" value="Wednesday" />
                                        <label class="form-check-label" for="wednesday">چهارشنبه</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="thursday" value="Thursday" />
                                        <label class="form-check-label" for="thursday">پنج‌شنبه</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="friday" value="Friday" />
                                        <label class="form-check-label" for="friday">جمعه</label>
                                    </div>
                                </div>
                                <input asp-for="Settings.WorkingDays" type="hidden" />
                                <span asp-validation-for="Settings.WorkingDays" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <h6 class="mb-3"><i class="bi bi-building"></i> اطلاعات سازمان</h6>
                    
                    <div class="mb-3">
                        <label asp-for="Settings.OrganizationName" class="form-label">نام سازمان <span class="text-danger">*</span></label>
                        <input asp-for="Settings.OrganizationName" class="form-control" placeholder="نام سازمان یا شرکت" />
                        <span asp-validation-for="Settings.OrganizationName" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Settings.OrganizationAddress" class="form-label">آدرس سازمان</label>
                        <textarea asp-for="Settings.OrganizationAddress" class="form-control" rows="3" placeholder="آدرس کامل سازمان"></textarea>
                        <span asp-validation-for="Settings.OrganizationAddress" class="text-danger"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.OrganizationPhone" class="form-label">تلفن سازمان</label>
                                <input asp-for="Settings.OrganizationPhone" class="form-control" placeholder="021-12345678" />
                                <span asp-validation-for="Settings.OrganizationPhone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.OrganizationEmail" class="form-label">ایمیل سازمان</label>
                                <input asp-for="Settings.OrganizationEmail" type="email" class="form-control" placeholder="<EMAIL>" />
                                <span asp-validation-for="Settings.OrganizationEmail" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <h6 class="mb-3"><i class="bi bi-toggles"></i> تنظیمات عملکردی</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="Settings.AutoRegisterEntry" class="form-check-input" type="checkbox" />
                                    <label asp-for="Settings.AutoRegisterEntry" class="form-check-label">ثبت خودکار ورود</label>
                                </div>
                                <div class="form-text">ورود کارمندان به صورت خودکار ثبت شود</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="Settings.AutoRegisterExit" class="form-check-input" type="checkbox" />
                                    <label asp-for="Settings.AutoRegisterExit" class="form-check-label">ثبت خودکار خروج</label>
                                </div>
                                <div class="form-text">خروج کارمندان به صورت خودکار ثبت شود</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="Settings.RequireApprovalForHourlyExit" class="form-check-input" type="checkbox" />
                                    <label asp-for="Settings.RequireApprovalForHourlyExit" class="form-check-label">نیاز به تأیید برای خروج ساعتی</label>
                                </div>
                                <div class="form-text">خروج ساعتی نیاز به تأیید مدیر داشته باشد</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Settings.MaxHourlyExitHours" class="form-label">حداکثر ساعت خروج ساعتی در روز</label>
                                <input asp-for="Settings.MaxHourlyExitHours" type="number" min="1" max="8" class="form-control" />
                                <span asp-validation-for="Settings.MaxHourlyExitHours" class="text-danger"></span>
                                <div class="form-text">حداکثر ساعات مجاز برای خروج ساعتی در یک روز</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره تنظیمات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="bi bi-arrow-clockwise"></i> بازنشانی
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> اطلاعات فعلی
                </h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-6">ساعت شروع:</dt>
                    <dd class="col-sm-6">@Model.Settings.WorkStartTime.ToString(@"hh\:mm")</dd>
                    
                    <dt class="col-sm-6">ساعت پایان:</dt>
                    <dd class="col-sm-6">@Model.Settings.WorkEndTime.ToString(@"hh\:mm")</dd>
                    
                    <dt class="col-sm-6">روزهای کاری:</dt>
                    <dd class="col-sm-6">@Model.Settings.GetWorkingDaysDisplay()</dd>
                    
                    <dt class="col-sm-6">آخرین به‌روزرسانی:</dt>
                    <dd class="col-sm-6">
                        @Model.Settings.LastUpdated.ToString("yyyy/MM/dd HH:mm")
                        @if (Model.Settings.LastUpdatedByUser != null)
                        {
                            <br><small class="text-muted">توسط: @Model.Settings.LastUpdatedByUser.Username</small>
                        }
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i> راهنما
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        تنظیمات بر روی تمام کارمندان اعمال می‌شود
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        تغییرات فوراً در سیستم اعمال می‌شود
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        روزهای کاری برای محاسبه حضور و غیاب استفاده می‌شود
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تنظیم روزهای کاری
        const workingDaysInput = document.querySelector('input[name="Settings.WorkingDays"]');
        const checkboxes = document.querySelectorAll('.form-check-input[type="checkbox"]');

        // بارگذاری روزهای کاری فعلی
        if (workingDaysInput.value) {
            const selectedDays = workingDaysInput.value.split(',');
            checkboxes.forEach(checkbox => {
                if (selectedDays.includes(checkbox.value)) {
                    checkbox.checked = true;
                }
            });
        }

        // به‌روزرسانی مقدار هنگام تغییر
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selectedDays = [];
                checkboxes.forEach(cb => {
                    if (cb.checked) {
                        selectedDays.push(cb.value);
                    }
                });
                workingDaysInput.value = selectedDays.join(',');
            });
        });
    });

    function resetForm() {
        if (confirm('آیا مطمئن هستید که می‌خواهید فرم را بازنشانی کنید؟')) {
            location.reload();
        }
    }
</script>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
