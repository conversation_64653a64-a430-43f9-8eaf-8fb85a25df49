using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Models;

namespace EmployeeTrafficControl.Data
{
    public static class SeedData
    {
        public static async Task InitializeAsync(ApplicationDbContext context)
        {
            // اطمینان از ایجاد دیتابیس
            await context.Database.EnsureCreatedAsync();

            // بررسی وجود داده‌های اولیه
            if (await context.Users.AnyAsync())
            {
                return; // داده‌ها از قبل وجود دارند
            }

            // ایجاد ساختمان پیش‌فرض
            var defaultBuilding = new Building
            {
                Name = "ساختمان اصلی"
            };
            context.Buildings.Add(defaultBuilding);
            await context.SaveChangesAsync();

            // ایجاد شغل پیش‌فرض
            var adminJob = new Job
            {
                Title = "مدیر سیستم",
                IsDriver = false
            };
            context.Jobs.Add(adminJob);

            var guardJob = new Job
            {
                Title = "نگهبان",
                IsDriver = false
            };
            context.Jobs.Add(guardJob);

            var driverJob = new Job
            {
                Title = "راننده",
                IsDriver = true
            };
            context.Jobs.Add(driverJob);

            await context.SaveChangesAsync();

            // ایجاد کارمند ادمین
            var adminEmployee = new Employee
            {
                FirstName = "مدیر",
                LastName = "سیستم",
                PersonnelCode = "ADMIN001",
                PhoneNumber = "09123456789",
                BuildingId = defaultBuilding.BuildingId,
                JobId = adminJob.JobId,
                HasDrivingLicense = false
            };
            context.Employees.Add(adminEmployee);
            await context.SaveChangesAsync();

            // ایجاد کاربر ادمین
            var adminUser = new User
            {
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456"), // رمز عبور: 123456
                Role = "Admin",
                EmployeeId = adminEmployee.EmployeeId,
                BuildingId = defaultBuilding.BuildingId
            };
            context.Users.Add(adminUser);

            // ایجاد کاربر نگهبان
            var guardEmployee = new Employee
            {
                FirstName = "علی",
                LastName = "احمدی",
                PersonnelCode = "GUARD001",
                PhoneNumber = "09123456790",
                BuildingId = defaultBuilding.BuildingId,
                JobId = guardJob.JobId,
                HasDrivingLicense = false
            };
            context.Employees.Add(guardEmployee);
            await context.SaveChangesAsync();

            var guardUser = new User
            {
                Username = "guard",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("123456"), // رمز عبور: 123456
                Role = "Guard",
                EmployeeId = guardEmployee.EmployeeId,
                BuildingId = defaultBuilding.BuildingId
            };
            context.Users.Add(guardUser);

            // ایجاد چند کارمند نمونه
            var employees = new List<Employee>
            {
                new Employee
                {
                    FirstName = "محمد",
                    LastName = "رضایی",
                    PersonnelCode = "EMP001",
                    PhoneNumber = "09123456791",
                    BuildingId = defaultBuilding.BuildingId,
                    JobId = driverJob.JobId,
                    HasDrivingLicense = true
                },
                new Employee
                {
                    FirstName = "فاطمه",
                    LastName = "محمدی",
                    PersonnelCode = "EMP002",
                    PhoneNumber = "09123456792",
                    BuildingId = defaultBuilding.BuildingId,
                    JobId = adminJob.JobId,
                    HasDrivingLicense = false
                },
                new Employee
                {
                    FirstName = "حسن",
                    LastName = "کریمی",
                    PersonnelCode = "EMP003",
                    PhoneNumber = "09123456793",
                    BuildingId = defaultBuilding.BuildingId,
                    JobId = guardJob.JobId,
                    HasDrivingLicense = true
                }
            };

            context.Employees.AddRange(employees);

            // ایجاد خودرو نمونه
            var car = new Car
            {
                PlateNumber = "12ب345-67",
                Model = "پژو پارس",
                Color = "سفید"
            };
            context.Cars.Add(car);

            // ایجاد تنظیمات سیستم پیش‌فرض
            var systemSettings = new SystemSettings
            {
                WorkStartTime = new TimeSpan(8, 0, 0), // 08:00
                WorkEndTime = new TimeSpan(16, 30, 0), // 16:30
                MaxLateMinutes = 15,
                MaxEarlyLeaveMinutes = 15,
                WorkingDays = "Saturday,Sunday,Monday,Tuesday,Wednesday",
                OrganizationName = "شرکت نمونه",
                OrganizationAddress = "تهران، خیابان آزادی، پلاک 123",
                OrganizationPhone = "021-12345678",
                OrganizationEmail = "<EMAIL>",
                AutoRegisterEntry = true,
                AutoRegisterExit = true,
                RequireApprovalForHourlyExit = false,
                MaxHourlyExitHours = 4,
                LastUpdated = DateTime.Now
            };
            context.SystemSettings.Add(systemSettings);

            await context.SaveChangesAsync();
        }
    }
}
