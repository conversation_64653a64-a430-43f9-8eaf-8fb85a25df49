using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Controllers
{
    [ApiController]
    [Route("api/traffic")]
    [IgnoreAntiforgeryToken]
    public class TrafficApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly DailyAttendanceService _attendanceService;

        public TrafficApiController(
            ApplicationDbContext context,
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService,
            DailyAttendanceService attendanceService)
        {
            _context = context;
            _authService = authService;
            _employeeStatusService = employeeStatusService;
            _attendanceService = attendanceService;
        }

        [HttpPost("register-entry")]
        public async Task<IActionResult> RegisterEntry([FromBody] RegisterEntryRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var results = new List<EntryResult>();

                foreach (var employeeId in request.EmployeeIds)
                {
                    try
                    {
                        // ثبت ورود در جدول حضور
                        var attendanceResult = await _attendanceService.RegisterCheckInAsync(employeeId, session.UserId);
                        
                        // ثبت وضعیت کارمند
                        var statusResult = await _employeeStatusService.RegisterEmployeeEntryAsync(employeeId, session.UserId);

                        results.Add(new EntryResult
                        {
                            EmployeeId = employeeId,
                            Success = attendanceResult && statusResult,
                            Message = attendanceResult && statusResult ? "ورود با موفقیت ثبت شد" : "خطا در ثبت ورود"
                        });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error registering entry for employee {employeeId}: {ex.Message}");
                        Console.WriteLine($"Stack trace: {ex.StackTrace}");
                        results.Add(new EntryResult
                        {
                            EmployeeId = employeeId,
                            Success = false,
                            Message = $"خطا: {ex.Message}"
                        });
                    }
                }

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                return Ok(new
                {
                    success = true,
                    message = $"ورود {successCount} نفر از {totalCount} نفر با موفقیت ثبت شد",
                    results = results
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"General error in register-entry: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return BadRequest(new { message = "خطا در ثبت ورود: " + ex.Message });
            }
        }

        [HttpPost("register-exit")]
        public async Task<IActionResult> RegisterExit([FromBody] RegisterExitRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var results = new List<ExitResult>();

                foreach (var employeeId in request.EmployeeIds)
                {
                    try
                    {
                        bool statusResult;
                        
                        // تعیین نوع خروج
                        switch (request.ExitType?.ToLower())
                        {
                            case "hourly":
                                statusResult = await _employeeStatusService.RegisterHourlyExitAsync(
                                    employeeId, session.UserId, request.ExitPermitNumber, request.Notes);
                                break;
                            case "mission":
                                statusResult = await _employeeStatusService.RegisterOfficialMissionAsync(
                                    employeeId, session.UserId, request.Notes);
                                break;
                            case "final":
                            default:
                                // ثبت خروج نهایی
                                var attendanceResult = await _attendanceService.RegisterCheckOutAsync(employeeId, session.UserId);
                                statusResult = await _employeeStatusService.RegisterEmployeeExitAsync(employeeId, session.UserId);
                                break;
                        }

                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = statusResult,
                            Message = statusResult ? "خروج با موفقیت ثبت شد" : "خطا در ثبت خروج"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = false,
                            Message = $"خطا: {ex.Message}"
                        });
                    }
                }

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                return Ok(new
                {
                    success = true,
                    message = $"خروج {successCount} نفر از {totalCount} نفر با موفقیت ثبت شد",
                    results = results
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطا در ثبت خروج: " + ex.Message });
            }
        }

        [HttpPost("register-group-exit")]
        public async Task<IActionResult> RegisterGroupExit([FromBody] RegisterGroupExitRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var results = new List<ExitResult>();

                foreach (var employeeId in request.EmployeeIds)
                {
                    try
                    {
                        // ثبت خروج گروهی (معمولاً ماموریت اداری)
                        var statusResult = await _employeeStatusService.UpdateEmployeeStatusAsync(
                            employeeId, 
                            Models.EmployeeCurrentStatus.OfficialMission, 
                            session.UserId, 
                            request.Notes, 
                            request.ExitPermitNumber, 
                            request.VehicleId);

                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = statusResult,
                            Message = statusResult ? "خروج گروهی با موفقیت ثبت شد" : "خطا در ثبت خروج گروهی"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = false,
                            Message = $"خطا: {ex.Message}"
                        });
                    }
                }

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                return Ok(new
                {
                    success = true,
                    message = $"خروج گروهی {successCount} نفر از {totalCount} نفر با موفقیت ثبت شد",
                    results = results
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطا در ثبت خروج گروهی: " + ex.Message });
            }
        }

        [HttpPost("register-car-entry")]
        public async Task<IActionResult> RegisterCarEntry([FromBody] RegisterCarEntryRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                if (string.IsNullOrWhiteSpace(request.PlateNumber))
                {
                    return BadRequest(new { message = "شماره پلاک اجباری است." });
                }

                // بررسی وجود خودرو در پارکینگ
                var existingCar = await _context.CarTrafficLogs
                    .FirstOrDefaultAsync(c => c.PlateNumber == request.PlateNumber && c.Status == CarStatus.InsideParking);

                if (existingCar != null)
                {
                    return BadRequest(new { message = "این خودرو قبلاً در پارکینگ ثبت شده است." });
                }

                // ایجاد رکورد ورود خودرو
                var carTrafficLog = new CarTrafficLog
                {
                    CarId = 1, // پیش‌فرض - باید از جدول Cars پیدا شود
                    EntryTime = DateTime.Now,
                    CurrentStatus = "در پارکینگ",
                    BuildingId = session.User.BuildingId ?? 1, // پیش‌فرض ساختمان اول
                    Notes = request.Notes
                };

                if (request.CarType == "employee" && request.DriverId.HasValue)
                {
                    carTrafficLog.DriverEmployeeId = request.DriverId.Value;
                }
                else
                {
                    carTrafficLog.DriverEmployeeId = 0; // مهمان
                }

                _context.CarTrafficLogs.Add(carTrafficLog);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "ورود خودرو با موفقیت ثبت شد",
                    carTrafficLogId = carTrafficLog.CarTrafficLogId
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering car entry: {ex.Message}");
                return BadRequest(new { message = "خطا در ثبت ورود خودرو: " + ex.Message });
            }
        }

        [HttpPost("register-car-exit")]
        public async Task<IActionResult> RegisterCarExit([FromBody] RegisterCarExitRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var carTrafficLog = await _context.CarTrafficLogs
                    .FirstOrDefaultAsync(c => c.CarTrafficLogId == request.CarTrafficLogId && c.CurrentStatus == "در پارکینگ");

                if (carTrafficLog == null)
                {
                    return BadRequest(new { message = "خودرو در پارکینگ یافت نشد." });
                }

                // ثبت خروج
                carTrafficLog.ExitTime = DateTime.Now;
                carTrafficLog.CurrentStatus = "خارج از پارکینگ";

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "خروج خودرو با موفقیت ثبت شد"
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering car exit: {ex.Message}");
                return BadRequest(new { message = "خطا در ثبت خروج خودرو: " + ex.Message });
            }
        }

        [HttpGet("get-available-passengers")]
        public async Task<IActionResult> GetAvailablePassengers(string exitType, int? buildingId)
        {
            try
            {
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized();
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session?.User == null)
                {
                    return Unauthorized();
                }

                var query = _context.EmployeeStatuses
                    .Include(es => es.Employee)
                    .ThenInclude(e => e.Job)
                    .Where(es => es.Date.Date == DateTime.Today &&
                               es.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding &&
                               es.Employee.BuildingId == buildingId);

                // فیلتر بر اساس نوع خروج
                if (exitType == "پولرسانی")
                {
                    // فقط نگهبان و خزانه‌دار
                    query = query.Where(es => es.Employee.Job.Title.Contains("نگهبان") ||
                                            es.Employee.Job.Title.Contains("خزانه"));
                }

                var passengers = await query
                    .Select(es => new
                    {
                        employeeId = es.Employee.EmployeeId,
                        firstName = es.Employee.FirstName,
                        lastName = es.Employee.LastName,
                        personnelCode = es.Employee.PersonnelCode,
                        jobTitle = es.Employee.Job.Title
                    })
                    .OrderBy(p => p.lastName)
                    .ToListAsync();

                return Ok(passengers);
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        private bool CanRegisterTraffic(string userRole)
        {
            return userRole == "Admin" || userRole == "Manager" || userRole == "Guard";
        }
    }

    // مدل‌های درخواست
    public class RegisterEntryRequest
    {
        public List<int> EmployeeIds { get; set; } = new();
    }

    public class RegisterExitRequest
    {
        public List<int> EmployeeIds { get; set; } = new();
        public string? ExitType { get; set; } // "hourly", "mission", "final"
        public string? ExitPermitNumber { get; set; }
        public string? Notes { get; set; }
    }

    public class RegisterGroupExitRequest
    {
        public List<int> EmployeeIds { get; set; } = new();
        public int? VehicleId { get; set; }
        public string? ExitPermitNumber { get; set; }
        public string? Notes { get; set; }
    }

    public class RegisterCarEntryRequest
    {
        public string PlateNumber { get; set; } = "";
        public string CarType { get; set; } = "employee"; // employee, guest
        public int? DriverId { get; set; }
        public string? GuestName { get; set; }
        public string? Notes { get; set; }
    }

    public class RegisterCarExitRequest
    {
        public int CarTrafficLogId { get; set; }
    }

    // مدل‌های پاسخ
    public class EntryResult
    {
        public int EmployeeId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ExitResult
    {
        public int EmployeeId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
