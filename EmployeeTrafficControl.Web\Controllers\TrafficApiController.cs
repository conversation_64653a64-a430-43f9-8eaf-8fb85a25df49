using Microsoft.AspNetCore.Mvc;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Controllers
{
    [ApiController]
    [Route("api/traffic")]
    public class TrafficApiController : ControllerBase
    {
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly DailyAttendanceService _attendanceService;

        public TrafficApiController(
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService,
            DailyAttendanceService attendanceService)
        {
            _authService = authService;
            _employeeStatusService = employeeStatusService;
            _attendanceService = attendanceService;
        }

        [HttpPost("register-entry")]
        public async Task<IActionResult> RegisterEntry([FromBody] RegisterEntryRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var results = new List<EntryResult>();

                foreach (var employeeId in request.EmployeeIds)
                {
                    try
                    {
                        // ثبت ورود در جدول حضور
                        var attendanceResult = await _attendanceService.RegisterCheckInAsync(employeeId, session.UserId);
                        
                        // ثبت وضعیت کارمند
                        var statusResult = await _employeeStatusService.RegisterEmployeeEntryAsync(employeeId, session.UserId);

                        results.Add(new EntryResult
                        {
                            EmployeeId = employeeId,
                            Success = attendanceResult && statusResult,
                            Message = attendanceResult && statusResult ? "ورود با موفقیت ثبت شد" : "خطا در ثبت ورود"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new EntryResult
                        {
                            EmployeeId = employeeId,
                            Success = false,
                            Message = $"خطا: {ex.Message}"
                        });
                    }
                }

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                return Ok(new
                {
                    success = true,
                    message = $"ورود {successCount} نفر از {totalCount} نفر با موفقیت ثبت شد",
                    results = results
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطا در ثبت ورود: " + ex.Message });
            }
        }

        [HttpPost("register-exit")]
        public async Task<IActionResult> RegisterExit([FromBody] RegisterExitRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var results = new List<ExitResult>();

                foreach (var employeeId in request.EmployeeIds)
                {
                    try
                    {
                        bool statusResult;
                        
                        // تعیین نوع خروج
                        switch (request.ExitType?.ToLower())
                        {
                            case "hourly":
                                statusResult = await _employeeStatusService.RegisterHourlyExitAsync(
                                    employeeId, session.UserId, request.ExitPermitNumber, request.Notes);
                                break;
                            case "mission":
                                statusResult = await _employeeStatusService.RegisterOfficialMissionAsync(
                                    employeeId, session.UserId, request.Notes);
                                break;
                            case "final":
                            default:
                                // ثبت خروج نهایی
                                var attendanceResult = await _attendanceService.RegisterCheckOutAsync(employeeId, session.UserId);
                                statusResult = await _employeeStatusService.RegisterEmployeeExitAsync(employeeId, session.UserId);
                                break;
                        }

                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = statusResult,
                            Message = statusResult ? "خروج با موفقیت ثبت شد" : "خطا در ثبت خروج"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = false,
                            Message = $"خطا: {ex.Message}"
                        });
                    }
                }

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                return Ok(new
                {
                    success = true,
                    message = $"خروج {successCount} نفر از {totalCount} نفر با موفقیت ثبت شد",
                    results = results
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطا در ثبت خروج: " + ex.Message });
            }
        }

        [HttpPost("register-group-exit")]
        public async Task<IActionResult> RegisterGroupExit([FromBody] RegisterGroupExitRequest request)
        {
            try
            {
                // بررسی احراز هویت
                var sessionToken = Request.Cookies["SessionToken"];
                if (string.IsNullOrEmpty(sessionToken))
                {
                    return Unauthorized(new { message = "احراز هویت نشده" });
                }

                var session = await _authService.ValidateSessionAsync(sessionToken);
                if (session == null)
                {
                    return Unauthorized(new { message = "نشست منقضی شده" });
                }

                // بررسی دسترسی
                if (!CanRegisterTraffic(session.User.Role))
                {
                    return Forbid("دسترسی غیرمجاز");
                }

                var results = new List<ExitResult>();

                foreach (var employeeId in request.EmployeeIds)
                {
                    try
                    {
                        // ثبت خروج گروهی (معمولاً ماموریت اداری)
                        var statusResult = await _employeeStatusService.UpdateEmployeeStatusAsync(
                            employeeId, 
                            Models.EmployeeCurrentStatus.OfficialMission, 
                            session.UserId, 
                            request.Notes, 
                            request.ExitPermitNumber, 
                            request.VehicleId);

                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = statusResult,
                            Message = statusResult ? "خروج گروهی با موفقیت ثبت شد" : "خطا در ثبت خروج گروهی"
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new ExitResult
                        {
                            EmployeeId = employeeId,
                            Success = false,
                            Message = $"خطا: {ex.Message}"
                        });
                    }
                }

                var successCount = results.Count(r => r.Success);
                var totalCount = results.Count;

                return Ok(new
                {
                    success = true,
                    message = $"خروج گروهی {successCount} نفر از {totalCount} نفر با موفقیت ثبت شد",
                    results = results
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "خطا در ثبت خروج گروهی: " + ex.Message });
            }
        }

        private bool CanRegisterTraffic(string userRole)
        {
            return userRole == "Admin" || userRole == "Manager" || userRole == "Guard";
        }
    }

    // مدل‌های درخواست
    public class RegisterEntryRequest
    {
        public List<int> EmployeeIds { get; set; } = new();
    }

    public class RegisterExitRequest
    {
        public List<int> EmployeeIds { get; set; } = new();
        public string? ExitType { get; set; } // "hourly", "mission", "final"
        public string? ExitPermitNumber { get; set; }
        public string? Notes { get; set; }
    }

    public class RegisterGroupExitRequest
    {
        public List<int> EmployeeIds { get; set; } = new();
        public int? VehicleId { get; set; }
        public string? ExitPermitNumber { get; set; }
        public string? Notes { get; set; }
    }

    // مدل‌های پاسخ
    public class EntryResult
    {
        public int EmployeeId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ExitResult
    {
        public int EmployeeId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
