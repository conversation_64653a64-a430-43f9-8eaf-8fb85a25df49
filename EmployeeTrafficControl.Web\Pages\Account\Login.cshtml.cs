using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly AuthenticationService _authService;

        public LoginModel(AuthenticationService authService)
        {
            _authService = authService;
        }

        [BindProperty]
        [Required(ErrorMessage = "نام کاربری الزامی است.")]
        [Display(Name = "نام کاربری")]
        public string Username { get; set; } = default!;

        [BindProperty]
        [Required(ErrorMessage = "رمز عبور الزامی است.")]
        [DataType(DataType.Password)]
        [Display(Name = "رمز عبور")]
        public string Password { get; set; } = default!;

        [BindProperty]
        [Display(Name = "مرا به خاطر بسپار")]
        public bool RememberMe { get; set; }

        public string? ReturnUrl { get; set; }

        public void OnGet(string? returnUrl = null)
        {
            ReturnUrl = returnUrl;
            
            // اگر کاربر قبلاً وارد شده، به داشبورد هدایت شود
            if (IsUserLoggedIn())
            {
                Response.Redirect("/Dashboard");
                return;
            }
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            ReturnUrl = returnUrl;

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // دریافت اطلاعات درخواست
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

                // احراز هویت
                var session = await _authService.LoginAsync(Username, Password, ipAddress, userAgent);

                if (session == null)
                {
                    ModelState.AddModelError(string.Empty, "نام کاربری یا رمز عبور اشتباه است.");
                    return Page();
                }

                // ذخیره توکن نشست در کوکی
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = HttpContext.Request.IsHttps,
                    SameSite = SameSiteMode.Strict,
                    Expires = RememberMe ? DateTime.Now.AddDays(30) : DateTime.Now.AddHours(8)
                };

                Response.Cookies.Append("SessionToken", session.SessionToken, cookieOptions);

                // ذخیره اطلاعات کاربر در Session
                HttpContext.Session.SetString("UserId", session.UserId.ToString());
                HttpContext.Session.SetString("Username", session.User.Username);
                HttpContext.Session.SetString("UserRole", session.User.Role);
                
                if (session.User.BuildingId.HasValue)
                {
                    HttpContext.Session.SetString("UserBuildingId", session.User.BuildingId.Value.ToString());
                }

                // هدایت به صفحه مقصد
                if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                {
                    return Redirect(returnUrl);
                }

                // هدایت بر اساس نقش کاربر
                return session.User.Role switch
                {
                    "Admin" => RedirectToPage("/Dashboard/Admin"),
                    "Manager" => RedirectToPage("/Dashboard/Manager"),
                    "Guard" => RedirectToPage("/Dashboard/Guard"),
                    _ => RedirectToPage("/Dashboard/User")
                };
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ورود به سیستم. لطفاً دوباره تلاش کنید.");
                // Log the exception
                Console.WriteLine($"Login error: {ex.Message}");
                return Page();
            }
        }

        private bool IsUserLoggedIn()
        {
            var sessionToken = Request.Cookies["SessionToken"];
            return !string.IsNullOrEmpty(sessionToken) && 
                   !string.IsNullOrEmpty(HttpContext.Session.GetString("UserId"));
        }
    }
}
