using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Admin
{
    public class SystemSettingsModel : PageModel
    {
        private readonly SystemSettingsService _systemSettingsService;

        public SystemSettingsModel(SystemSettingsService systemSettingsService)
        {
            _systemSettingsService = systemSettingsService;
        }

        [BindProperty]
        public SystemSettings Settings { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی دسترسی ادمین
            var userRole = HttpContext.Session.GetString("UserRole");
            if (userRole != "Admin")
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            Settings = await _systemSettingsService.GetSystemSettingsAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // بررسی دسترسی ادمین
            var userRole = HttpContext.Session.GetString("UserRole");
            if (userRole != "Admin")
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            // اعتبارسنجی سفارشی
            if (Settings.WorkStartTime >= Settings.WorkEndTime)
            {
                ModelState.AddModelError("Settings.WorkEndTime", "ساعت پایان کار باید بعد از ساعت شروع کار باشد.");
            }

            if (string.IsNullOrEmpty(Settings.WorkingDays))
            {
                ModelState.AddModelError("Settings.WorkingDays", "حداقل یک روز کاری باید انتخاب شود.");
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // دریافت شناسه کاربر فعلی
                var userIdString = HttpContext.Session.GetString("UserId");
                if (!int.TryParse(userIdString, out int userId))
                {
                    TempData["ErrorMessage"] = "خطا در شناسایی کاربر.";
                    return Page();
                }

                // به‌روزرسانی تنظیمات
                bool result = await _systemSettingsService.UpdateSystemSettingsAsync(Settings, userId);

                if (result)
                {
                    TempData["SuccessMessage"] = "تنظیمات سیستم با موفقیت به‌روزرسانی شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "خطا در ذخیره تنظیمات.";
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره تنظیمات: " + ex.Message);
                return Page();
            }
        }
    }
}
