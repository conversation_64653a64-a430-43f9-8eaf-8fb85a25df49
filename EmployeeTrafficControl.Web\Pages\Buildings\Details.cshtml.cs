using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    public class DetailsModel : PageModel
    {
        private readonly BuildingService _buildingService;
        private readonly EmployeeService _employeeService;

        public DetailsModel(BuildingService buildingService, EmployeeService employeeService)
        {
            _buildingService = buildingService;
            _employeeService = employeeService;
        }

        public Building Building { get; set; } = default!;
        public IList<Employee> Employees { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Building = await _buildingService.GetBuildingByIdAsync(id);

            if (Building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Get employees in this building
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            Employees = allEmployees.Where(e => e.BuildingId == id).ToList();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var building = await _buildingService.GetBuildingByIdAsync(id);
            if (building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            // Check if there are employees in this building
            var allEmployees = await _employeeService.GetAllEmployeesAsync();
            var employeesInBuilding = allEmployees.Where(e => e.BuildingId == id).ToList();

            if (employeesInBuilding.Any())
            {
                TempData["ErrorMessage"] = $"امکان حذف این ساختمان وجود ندارد زیرا {employeesInBuilding.Count} کارمند در این ساختمان کار می‌کنند.";
                return RedirectToPage("./Details", new { id });
            }

            try
            {
                await _buildingService.DeleteBuildingAsync(id);
                TempData["SuccessMessage"] = "ساختمان با موفقیت حذف شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف ساختمان: " + ex.Message;
                return RedirectToPage("./Details", new { id });
            }
        }
    }
}
