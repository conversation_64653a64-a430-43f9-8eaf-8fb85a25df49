using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    public class IndexModel : PageModel
    {
        private readonly CarService _carService;

        public IndexModel(CarService carService)
        {
            _carService = carService;
        }

        public IList<Car> Cars { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Cars = await _carService.GetAllCarsAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var car = await _carService.GetCarByIdAsync(id);
            if (car != null)
            {
                await _carService.DeleteCarAsync(id);
                TempData["SuccessMessage"] = "خودرو با موفقیت حذف شد.";
            }
            else
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
            }

            return RedirectToPage();
        }
    }
}
