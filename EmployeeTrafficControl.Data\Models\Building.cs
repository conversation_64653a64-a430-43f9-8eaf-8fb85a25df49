﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Models
{
    public class Building
    {
        [Key]
        [Display(Name = "شناسه ساختمان")]
        public int BuildingId { get; set; }

        [Required(ErrorMessage = "نام ساختمان اجباری است.")]
        [StringLength(100, ErrorMessage = "نام ساختمان حداکثر 100 کاراکتر باشد.")]
        [Display(Name = "نام ساختمان")]
        public string Name { get; set; }

        [StringLength(4000, ErrorMessage = "توضیحات نمی‌تواند بیشتر از 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Description { get; set; }

        // Navigation properties (DisplayName for navigation properties is less common but can be added if needed for UI)
        public ICollection<Employee> Employees { get; set; } = new List<Employee>();
        public ICollection<Car> Cars { get; set; } = new List<Car>();
        public WorkingHoursSetting? WorkingHoursSetting { get; set; }
        public ICollection<TrafficLog> TrafficLogs { get; set; } = new List<TrafficLog>();
        public ICollection<CarTrafficLog> CarTrafficLogs { get; set; } = new List<CarTrafficLog>();
        public ICollection<GuestCarTrafficLog> GuestCarTrafficLogs { get; set; } = new List<GuestCarTrafficLog>();
        public ICollection<User> Users { get; set; } = new List<User>();
    }
}