using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    public class EditModel : PageModel
    {
        private readonly CarService _carService;

        public EditModel(CarService carService)
        {
            _carService = carService;
        }

        [BindProperty]
        public Car Car { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Car = await _carService.GetCarByIdAsync(id);

            if (Car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Check if car plate number already exists (excluding current car)
            bool carExists = await _carService.CarExistsAsync(Car.PlateNumber, Car.CarId);
            if (carExists)
            {
                ModelState.AddModelError("Car.PlateNumber", "شماره پلاک وارد شده قبلاً ثبت شده است.");
                return Page();
            }

            try
            {
                await _carService.UpdateCarAsync(Car);
                TempData["SuccessMessage"] = "اطلاعات خودرو با موفقیت به‌روزرسانی شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                return Page();
            }
        }
    }
}
