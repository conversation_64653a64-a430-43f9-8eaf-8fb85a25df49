@page
@model EmployeeTrafficControl.Web.Pages.Buildings.IndexModel
@{
    ViewData["Title"] = "لیست ساختمان‌ها";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>لیست ساختمان‌ها</h1>
        <a asp-page="Create" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> افزودن ساختمان جدید
        </a>
    </div>
</div>

@if (Model.Buildings == null || !Model.Buildings.Any())
{
    <div class="alert alert-info text-center">
        <i class="bi bi-info-circle"></i>
        <p class="mb-0">هیچ ساختمانی یافت نشد.</p>
        <a asp-page="Create" class="btn btn-primary mt-2">افزودن اولین ساختمان</a>
    </div>
}
else
{
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>نام ساختمان</th>
                            <th>توضیحات</th>
                            <th>تعداد کارمندان</th>
                            <th class="text-center">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var building in Model.Buildings)
                        {
                            <tr>
                                <td>
                                    <strong>@building.Name</strong>
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(building.Description))
                                    {
                                        <span>@building.Description</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">بدون توضیحات</span>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@building.Employees.Count نفر</span>
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a asp-page="Edit" asp-route-id="@building.BuildingId" class="btn btn-sm btn-outline-primary" title="ویرایش">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-page="Details" asp-route-id="@building.BuildingId" class="btn btn-sm btn-outline-info" title="جزئیات">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <form asp-page="Delete" asp-route-id="@building.BuildingId" method="post" class="d-inline" 
                                              onsubmit="return confirmDelete('آیا مطمئن هستید که می‌خواهید این ساختمان را حذف کنید؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <p class="text-muted">
                    نمایش @Model.Buildings.Count مورد
                </p>
            </div>
            <div class="col-md-6 text-end">
                <a asp-page="Create" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> افزودن ساختمان جدید
                </a>
            </div>
        </div>
    </div>
}
