﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Models
{
    public class User
    {
        [Key]
        [Display(Name = "شناسه کاربر")]
        public int UserId { get; set; }

        [Display(Name = "شناسه کارمند مرتبط")]
        public int? EmployeeId { get; set; }

        [Required(ErrorMessage = "نام کاربری اجباری است.")]
        [StringLength(50, ErrorMessage = "نام کاربری حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نام کاربری")]
        public string Username { get; set; }

        [Required(ErrorMessage = "رمز عبور اجباری است.")]
        [StringLength(256, ErrorMessage = "رمز عبور حداکثر 256 کاراکتر باشد.")]
        [Display(Name = "رمز عبور")]
        public string PasswordHash { get; set; }

        [Required(ErrorMessage = "نقش کاربر اجباری است.")]
        [StringLength(50, ErrorMessage = "نقش کاربر حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "نقش")]
        public string Role { get; set; }

        [Display(Name = "ساختمان مرتبط")]
        public int? BuildingId { get; set; }

        [Display(Name = "فعال")]
        public bool IsActive { get; set; } = true;

        [ForeignKey("EmployeeId")]
        [Display(Name = "کارمند")]
        public Employee? Employee { get; set; }

        [ForeignKey("BuildingId")]
        [Display(Name = "ساختمان")]
        public Building? Building { get; set; }
    }
}