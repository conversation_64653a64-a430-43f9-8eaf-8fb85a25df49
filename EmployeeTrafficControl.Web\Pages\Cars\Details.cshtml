@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Cars.DetailsModel
@{
    ViewData["Title"] = "جزئیات خودرو";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1>جزئیات خودرو</h1>
        <div>
            <a asp-page="Edit" asp-route-id="@Model.Car.CarId" class="btn btn-primary">
                <i class="bi bi-pencil"></i> ویرایش
            </a>
            <a asp-page="Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به لیست
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">@Model.Car.PlateNumber</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">شناسه:</dt>
                    <dd class="col-sm-9">@Model.Car.CarId</dd>
                    
                    <dt class="col-sm-3">شماره پلاک:</dt>
                    <dd class="col-sm-9">
                        <strong class="text-primary">@Model.Car.PlateNumber</strong>
                    </dd>
                    
                    <dt class="col-sm-3">مدل:</dt>
                    <dd class="col-sm-9">
                        @if (!string.IsNullOrEmpty(Model.Car.Model))
                        {
                            <span>@Model.Car.Model</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">رنگ:</dt>
                    <dd class="col-sm-9">
                        @if (!string.IsNullOrEmpty(Model.Car.Color))
                        {
                            <span>@Model.Car.Color</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">نوع:</dt>
                    <dd class="col-sm-9">
                        @if (!string.IsNullOrEmpty(Model.Car.Type))
                        {
                            <span>@Model.Car.Type</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>
                    
                    <dt class="col-sm-3">ساختمان:</dt>
                    <dd class="col-sm-9">
                        @if (Model.Car.Building != null)
                        {
                            <span class="badge bg-primary">@Model.Car.Building.Name</span>
                        }
                        else
                        {
                            <span class="text-muted">نامشخص</span>
                        }
                    </dd>

                    <dt class="col-sm-3">ظرفیت سرنشین:</dt>
                    <dd class="col-sm-9">
                        <span class="badge bg-info">@Model.Car.PassengerCapacity نفر</span>
                    </dd>

                    <dt class="col-sm-3">نوع خودرو:</dt>
                    <dd class="col-sm-9">
                        @if (Model.Car.IsMoneyTransport)
                        {
                            <span class="badge bg-warning text-dark">
                                <i class="bi bi-shield-check"></i> پولرسان
                            </span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">عادی</span>
                        }
                    </dd>
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">عملیات</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-page="Edit" asp-route-id="@Model.Car.CarId" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> ویرایش خودرو
                    </a>
                    <hr>
                    <form asp-page="Delete" asp-route-id="@Model.Car.CarId" method="post" 
                          onsubmit="return confirmDelete('آیا مطمئن هستید که می‌خواهید این خودرو را حذف کنید؟')">
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="bi bi-trash"></i> حذف خودرو
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">مشخصات فنی</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h4 class="text-info">@Model.Car.PassengerCapacity</h4>
                        <p class="mb-0">ظرفیت سرنشین</p>
                    </div>
                </div>
                
                @if (!string.IsNullOrEmpty(Model.Car.Model) || !string.IsNullOrEmpty(Model.Car.Color) || !string.IsNullOrEmpty(Model.Car.Type))
                {
                    <hr>
                    <ul class="list-unstyled">
                        @if (!string.IsNullOrEmpty(Model.Car.Model))
                        {
                            <li><i class="bi bi-car-front text-primary"></i> مدل: @Model.Car.Model</li>
                        }
                        @if (!string.IsNullOrEmpty(Model.Car.Color))
                        {
                            <li><i class="bi bi-palette text-success"></i> رنگ: @Model.Car.Color</li>
                        }
                        @if (!string.IsNullOrEmpty(Model.Car.Type))
                        {
                            <li><i class="bi bi-tag text-warning"></i> نوع: @Model.Car.Type</li>
                        }
                    </ul>
                }
            </div>
        </div>
    </div>
</div>
