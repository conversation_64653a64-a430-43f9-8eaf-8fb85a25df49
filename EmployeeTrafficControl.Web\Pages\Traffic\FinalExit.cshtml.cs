using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class FinalExitModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly SystemSettingsService _systemSettingsService;

        public FinalExitModel(
            ApplicationDbContext context,
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService,
            SystemSettingsService systemSettingsService)
        {
            _context = context;
            _authService = authService;
            _employeeStatusService = employeeStatusService;
            _systemSettingsService = systemSettingsService;
        }

        public List<EmployeeStatus> PresentEmployees { get; set; } = new();
        public List<DailyAttendance> TodayAttendances { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();
        public List<SelectListItem> Jobs { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? JobId { get; set; }

        public TimeSpan WorkStartTime { get; set; }
        public TimeSpan WorkEndTime { get; set; }
        public int FinalExitCount { get; set; }
        public int EarlyExitCount { get; set; }
        public int OvertimeCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            if (!CanRegisterTraffic(session.User.Role))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(session.User);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(User currentUser)
        {
            // بارگذاری تنظیمات ساعت کاری
            WorkStartTime = await _systemSettingsService.GetWorkStartTimeAsync();
            WorkEndTime = await _systemSettingsService.GetWorkEndTimeAsync();

            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری لیست مشاغل
            var jobs = await _context.Jobs.OrderBy(j => j.Title).ToListAsync();
            Jobs = jobs.Select(j => new SelectListItem
            {
                Value = j.JobId.ToString(),
                Text = j.Title,
                Selected = j.JobId == JobId
            }).ToList();

            // دریافت کارمندان حاضر در ساختمان
            PresentEmployees = await _employeeStatusService.GetPresentEmployeesAsync(BuildingId);

            // اعمال فیلتر شغل
            if (JobId.HasValue)
            {
                PresentEmployees = PresentEmployees.Where(es => es.Employee.JobId == JobId.Value).ToList();
            }

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                PresentEmployees = PresentEmployees.Where(es =>
                    es.Employee.FirstName.ToLower().Contains(searchLower) ||
                    es.Employee.LastName.ToLower().Contains(searchLower) ||
                    es.Employee.PersonnelCode.ToLower().Contains(searchLower)).ToList();
            }

            // بارگذاری حضور امروز
            var today = DateTime.Today;
            var employeeIds = PresentEmployees.Select(es => es.EmployeeId).ToList();

            TodayAttendances = await _context.DailyAttendances
                                           .Where(da => da.Date.Date == today && employeeIds.Contains(da.EmployeeId))
                                           .ToListAsync();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == today);

            if (accessibleBuildingIds != null)
            {
                query = query.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == BuildingId.Value);
            }

            if (JobId.HasValue)
            {
                query = query.Where(es => es.Employee.JobId == JobId.Value);
            }

            var statuses = await query.ToListAsync();

            FinalExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice);

            // محاسبه تعداد کسانی که زودتر رفته‌اند
            var currentTime = DateTime.Now.TimeOfDay;
            if (currentTime < WorkEndTime)
            {
                EarlyExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice);
            }
            else
            {
                EarlyExitCount = 0;
            }

            // محاسبه اضافه کار (کسانی که هنوز در ساختمان هستند و ساعت کاری تمام شده)
            if (currentTime > WorkEndTime)
            {
                OvertimeCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding);
            }
            else
            {
                OvertimeCount = 0;
            }
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(string userRole)
        {
            return userRole == "Admin" || userRole == "Manager" || userRole == "Guard";
        }
    }
}
