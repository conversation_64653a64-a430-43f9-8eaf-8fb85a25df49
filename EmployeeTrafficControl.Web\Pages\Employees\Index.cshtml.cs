using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    public class IndexModel : PageModel
    {
        private readonly EmployeeService _employeeService;

        public IndexModel(EmployeeService employeeService)
        {
            _employeeService = employeeService;
        }

        public IList<Employee> Employees { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Employees = await _employeeService.GetAllEmployeesAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var employee = await _employeeService.GetEmployeeByIdAsync(id);
            if (employee != null)
            {
                await _employeeService.DeleteEmployeeAsync(id);
                TempData["SuccessMessage"] = "کارمند با موفقیت حذف شد.";
            }
            else
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
            }

            return RedirectToPage();
        }
    }
}
