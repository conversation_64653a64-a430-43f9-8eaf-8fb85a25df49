using System.ComponentModel.DataAnnotations;

namespace EmployeeTrafficControl.Models
{
    /// <summary>
    /// وضعیت خودرو در پارکینگ
    /// </summary>
    public enum CarStatus
    {
        [Display(Name = "در پارکینگ")]
        InsideParking = 1,

        [Display(Name = "خارج از پارکینگ")]
        OutsideParking = 2,

        [Display(Name = "در حال ورود")]
        Entering = 3,

        [Display(Name = "در حال خروج")]
        Exiting = 4,

        [Display(Name = "مسدود شده")]
        Blocked = 5,

        [Display(Name = "نامشخص")]
        Unknown = 0
    }

    /// <summary>
    /// متدهای کمکی برای enum وضعیت خودرو
    /// </summary>
    public static class CarStatusExtensions
    {
        /// <summary>
        /// دریافت نام نمایشی وضعیت خودرو
        /// </summary>
        public static string GetDisplayName(this CarStatus status)
        {
            return status switch
            {
                CarStatus.InsideParking => "در پارکینگ",
                CarStatus.OutsideParking => "خارج از پارکینگ",
                CarStatus.Entering => "در حال ورود",
                CarStatus.Exiting => "در حال خروج",
                CarStatus.Blocked => "مسدود شده",
                CarStatus.Unknown => "نامشخص",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// دریافت کلاس CSS برای badge وضعیت خودرو
        /// </summary>
        public static string GetStatusBadgeClass(this CarStatus status)
        {
            return status switch
            {
                CarStatus.InsideParking => "bg-success",
                CarStatus.OutsideParking => "bg-secondary",
                CarStatus.Entering => "bg-info",
                CarStatus.Exiting => "bg-warning",
                CarStatus.Blocked => "bg-danger",
                CarStatus.Unknown => "bg-dark",
                _ => "bg-secondary"
            };
        }

        /// <summary>
        /// دریافت آیکون برای وضعیت خودرو
        /// </summary>
        public static string GetStatusIcon(this CarStatus status)
        {
            return status switch
            {
                CarStatus.InsideParking => "bi-car-front-fill",
                CarStatus.OutsideParking => "bi-car-front",
                CarStatus.Entering => "bi-arrow-right-circle",
                CarStatus.Exiting => "bi-arrow-left-circle",
                CarStatus.Blocked => "bi-exclamation-triangle-fill",
                CarStatus.Unknown => "bi-question-circle",
                _ => "bi-car-front"
            };
        }

        /// <summary>
        /// بررسی اینکه آیا خودرو در پارکینگ است یا نه
        /// </summary>
        public static bool IsInside(this CarStatus status)
        {
            return status == CarStatus.InsideParking || status == CarStatus.Entering;
        }

        /// <summary>
        /// بررسی اینکه آیا خودرو خارج از پارکینگ است یا نه
        /// </summary>
        public static bool IsOutside(this CarStatus status)
        {
            return status == CarStatus.OutsideParking || status == CarStatus.Exiting;
        }

        /// <summary>
        /// تبدیل string به CarStatus
        /// </summary>
        public static CarStatus FromString(string status)
        {
            return status?.ToLower() switch
            {
                "inside" => CarStatus.InsideParking,
                "outside" => CarStatus.OutsideParking,
                "entering" => CarStatus.Entering,
                "exiting" => CarStatus.Exiting,
                "blocked" => CarStatus.Blocked,
                _ => CarStatus.Unknown
            };
        }

        /// <summary>
        /// تبدیل CarStatus به string برای ذخیره در دیتابیس
        /// </summary>
        public static string ToDbString(this CarStatus status)
        {
            return status switch
            {
                CarStatus.InsideParking => "Inside",
                CarStatus.OutsideParking => "Outside",
                CarStatus.Entering => "Entering",
                CarStatus.Exiting => "Exiting",
                CarStatus.Blocked => "Blocked",
                _ => "Unknown"
            };
        }
    }
}
