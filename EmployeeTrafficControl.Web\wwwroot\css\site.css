/* RTL and Persian language styles */
html {
  font-size: 14px;
  direction: rtl;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Vazirmatn', '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
}

.btn-primary {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-outline-dark {
  color: #212529;
  border-color: #212529;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #212529;
  border-color: #212529;
}

a.navbar-brand {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a {
  color: #0077cc;
}

.btn-primary {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top {
  border-top: 1px solid #e5e5e5;
}
.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy {
  font-size: 1rem;
  line-height: inherit;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

/* Custom styles for Persian/RTL */
.table {
  direction: rtl;
  text-align: right;
}

.form-control, .form-select {
  direction: rtl;
  text-align: right;
}

.navbar-nav {
  direction: rtl;
}

.navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

/* Alert styles */
.alert {
  direction: rtl;
  text-align: right;
}

/* Card styles */
.card {
  direction: rtl;
  text-align: right;
}

/* Pagination styles */
.pagination {
  direction: rtl;
}

/* Modal styles */
.modal-content {
  direction: rtl;
  text-align: right;
}

/* Form validation styles */
.field-validation-error {
  color: #dc3545;
  font-size: 0.875em;
}

.validation-summary-errors {
  color: #dc3545;
}

.input-validation-error {
  border-color: #dc3545;
}

/* Loading spinner */
.loading {
  text-align: center;
  padding: 2rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Custom button spacing */
.btn + .btn {
  margin-right: 0.5rem;
}

/* Table responsive */
.table-responsive {
  direction: rtl;
}

/* Search form */
.search-form {
  margin-bottom: 1rem;
}

.search-form .form-control {
  display: inline-block;
  width: auto;
  margin-left: 0.5rem;
}

/* Action buttons in tables */
.action-buttons {
  white-space: nowrap;
}

.action-buttons .btn {
  margin-left: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Page header */
.page-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.page-header h1 {
  margin-bottom: 0;
}

/* Form sections */
.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

/* Status badges */
.status-active {
  background-color: #28a745;
}

.status-inactive {
  background-color: #dc3545;
}

/* Custom spacing */
.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .action-buttons .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
  
  .navbar-nav {
    text-align: center;
  }
}
