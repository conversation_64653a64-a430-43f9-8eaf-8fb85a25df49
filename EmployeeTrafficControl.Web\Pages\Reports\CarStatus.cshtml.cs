using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    public class CarStatusModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;

        public CarStatusModel(
            ApplicationDbContext context,
            AuthenticationService authService)
        {
            _context = context;
            _authService = authService;
        }

        public List<CarTrafficLog> Cars { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? CarType { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        // آمار
        public int InsideCount { get; set; }
        public int OutsideCount { get; set; }
        public int EmployeeCarCount { get; set; }
        public int GuestCarCount { get; set; }
        public int BlockedCount { get; set; }
        public int TotalCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            try
            {
                await LoadDataAsync(session.User);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(User currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری خودروها
            var carsQuery = _context.CarTrafficLogs
                                   .Include(c => c.DriverEmployee)
                                   .Include(c => c.Building)
                                   .AsQueryable();

            if (accessibleBuildingIds != null)
            {
                carsQuery = carsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            // اعمال فیلترها
            if (BuildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            if (StatusFilter.HasValue)
            {
                var status = (CarStatus)StatusFilter.Value;
                carsQuery = carsQuery.Where(c => c.Status == status);
            }

            if (!string.IsNullOrEmpty(CarType))
            {
                if (CarType == "employee")
                {
                    carsQuery = carsQuery.Where(c => c.DriverEmployeeId != null);
                }
                else if (CarType == "guest")
                {
                    carsQuery = carsQuery.Where(c => c.DriverEmployeeId == null);
                }
            }

            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                carsQuery = carsQuery.Where(c =>
                    c.PlateNumber.ToLower().Contains(searchLower) ||
                    (c.DriverEmployee != null && 
                     (c.DriverEmployee.FirstName.ToLower().Contains(searchLower) ||
                      c.DriverEmployee.LastName.ToLower().Contains(searchLower) ||
                      c.DriverEmployee.PersonnelCode.ToLower().Contains(searchLower))) ||
                    (c.GuestName != null && c.GuestName.ToLower().Contains(searchLower)));
            }

            Cars = await carsQuery.OrderByDescending(c => c.EntryTime).ToListAsync();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var statsQuery = _context.CarTrafficLogs.AsQueryable();

            if (accessibleBuildingIds != null)
            {
                statsQuery = statsQuery.Where(c => accessibleBuildingIds.Contains(c.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                statsQuery = statsQuery.Where(c => c.BuildingId == BuildingId.Value);
            }

            var allCars = await statsQuery.ToListAsync();

            InsideCount = allCars.Count(c => c.Status == CarStatus.InsideParking);
            OutsideCount = allCars.Count(c => c.Status == CarStatus.OutsideParking);
            EmployeeCarCount = allCars.Count(c => c.DriverEmployeeId != null);
            GuestCarCount = allCars.Count(c => c.DriverEmployeeId == null);
            BlockedCount = allCars.Count(c => c.Status == CarStatus.Blocked);
            TotalCount = allCars.Count;
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }
    }
}
