using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;

namespace EmployeeTrafficControl.Web.Pages.Test
{
    public class AddSampleDataModel : PageModel
    {
        private readonly ApplicationDbContext _context;

        public AddSampleDataModel(ApplicationDbContext context)
        {
            _context = context;
        }

        public string Message { get; set; } = "";
        public List<Car> Cars { get; set; } = new();
        public List<CarTrafficLog> CarTrafficLogs { get; set; } = new();

        public async Task OnGetAsync()
        {
            await LoadDataAsync();
        }

        public async Task<IActionResult> OnPostAddSampleCarAsync()
        {
            try
            {
                // بررسی اینکه آیا ساختمان وجود دارد
                var building = await _context.Buildings.FirstOrDefaultAsync();
                if (building == null)
                {
                    Message = "ابتدا باید ساختمان ایجاد شود.";
                    await LoadDataAsync();
                    return Page();
                }

                // ایجاد خودرو نمونه
                var car = new Car
                {
                    PlateNumber = $"12ج{Random.Shared.Next(100, 999)}34",
                    Model = "پراید",
                    Color = "سفید",
                    Type = "شخصی",
                    PassengerCapacity = 4,
                    BuildingId = building.BuildingId
                };

                _context.Cars.Add(car);
                await _context.SaveChangesAsync();

                Message = $"خودرو با پلاک {car.PlateNumber} اضافه شد.";
            }
            catch (Exception ex)
            {
                Message = $"خطا در اضافه کردن خودرو: {ex.Message}";
            }

            await LoadDataAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAddSampleCarTrafficAsync()
        {
            try
            {
                // بررسی اینکه آیا خودرو وجود دارد
                var car = await _context.Cars.FirstOrDefaultAsync();
                if (car == null)
                {
                    Message = "ابتدا باید خودرو ایجاد شود.";
                    await LoadDataAsync();
                    return Page();
                }

                // بررسی اینکه آیا کارمند وجود دارد
                var employee = await _context.Employees.FirstOrDefaultAsync();
                if (employee == null)
                {
                    Message = "ابتدا باید کارمند ایجاد شود.";
                    await LoadDataAsync();
                    return Page();
                }

                // ایجاد تردد خودرو نمونه
                var carTrafficLog = new CarTrafficLog
                {
                    CarId = car.CarId,
                    DriverEmployeeId = employee.EmployeeId,
                    BuildingId = car.BuildingId,
                    EntryTime = DateTime.Now.AddHours(-2), // 2 ساعت پیش وارد شده
                    CurrentStatus = "در پارکینگ",
                    Notes = "تردد نمونه برای تست"
                };

                _context.CarTrafficLogs.Add(carTrafficLog);
                await _context.SaveChangesAsync();

                Message = $"تردد خودرو با پلاک {car.PlateNumber} اضافه شد.";
            }
            catch (Exception ex)
            {
                Message = $"خطا در اضافه کردن تردد خودرو: {ex.Message}";
            }

            await LoadDataAsync();
            return Page();
        }

        private async Task LoadDataAsync()
        {
            Cars = await _context.Cars
                .Include(c => c.Building)
                .OrderBy(c => c.PlateNumber)
                .ToListAsync();

            CarTrafficLogs = await _context.CarTrafficLogs
                .Include(c => c.Car)
                .Include(c => c.DriverEmployee)
                .OrderByDescending(c => c.EntryTime)
                .Take(10)
                .ToListAsync();
        }
    }
}
