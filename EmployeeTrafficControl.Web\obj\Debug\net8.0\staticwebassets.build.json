{"Version": 1, "Hash": "LfF122ApwV4xkVOKuZ1Fpbyg02/wEZAsUMwFvHEXGQk=", "Source": "EmployeeTrafficControl.Web", "BasePath": "_content/EmployeeTrafficControl.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "EmployeeTrafficControl.Web\\wwwroot", "Source": "EmployeeTrafficControl.Web", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "Pattern": "**"}], "Assets": [{"Identity": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\app.css", "SourceId": "EmployeeTrafficControl.Web", "SourceType": "Discovered", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "da95v2qkru", "Integrity": "u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css"}, {"Identity": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "EmployeeTrafficControl.Web", "SourceType": "Discovered", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css"}, {"Identity": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "EmployeeTrafficControl.Web", "SourceType": "Discovered", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\css\\site.css", "SourceId": "EmployeeTrafficControl.Web", "SourceType": "Discovered", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "327piihe2t", "Integrity": "Baaj0edSb1HDoeqHr5ytNz7lYhjEJDK+A05C2SlHVuQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\favicon.png", "SourceId": "EmployeeTrafficControl.Web", "SourceType": "Discovered", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png"}, {"Identity": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\js\\site.js", "SourceId": "EmployeeTrafficControl.Web", "SourceType": "Discovered", "ContentRoot": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\", "BasePath": "_content/EmployeeTrafficControl.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "12f8cendp1", "Integrity": "OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}], "Endpoints": [{"Route": "app.css", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/site.327piihe2t.css", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3359"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Baaj0edSb1HDoeqHr5ytNz7lYhjEJDK+A05C2SlHVuQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 13:08:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "327piihe2t"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-Baaj0edSb1HDoeqHr5ytNz7lYhjEJDK+A05C2SlHVuQ="}]}, {"Route": "css/site.css", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3359"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Baaj0edSb1HDoeqHr5ytNz7lYhjEJDK+A05C2SlHVuQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 13:08:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Baaj0edSb1HDoeqHr5ytNz7lYhjEJDK+A05C2SlHVuQ="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/site.12f8cendp1.js", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 12:42:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "12f8cendp1"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc="}]}, {"Route": "js/site.js", "AssetFile": "G:\\Programing\\Projects\\Web\\asp.net\\Blazor\\EmployeeTrafficControl\\EmployeeTrafficControl.Web\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 12:42:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc="}]}]}