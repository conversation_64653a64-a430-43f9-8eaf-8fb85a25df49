using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    public class EditModel : PageModel
    {
        private readonly EmployeeService _employeeService;
        private readonly BuildingService _buildingService;
        private readonly JobService _jobService;

        public EditModel(EmployeeService employeeService, BuildingService buildingService, JobService jobService)
        {
            _employeeService = employeeService;
            _buildingService = buildingService;
            _jobService = jobService;
        }

        [BindProperty]
        public Employee Employee { get; set; } = default!;

        public SelectList Buildings { get; set; } = default!;
        public SelectList Jobs { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int id)
        {
            Employee = await _employeeService.GetEmployeeByIdAsync(id);

            if (Employee == null)
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            await LoadSelectLists();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadSelectLists();
                return Page();
            }

            // Check if personnel code already exists (excluding current employee)
            bool personnelCodeExists = await _employeeService.EmployeeCodeExistsAsync(Employee.PersonnelCode, Employee.EmployeeId);
            if (personnelCodeExists)
            {
                ModelState.AddModelError("Employee.PersonnelCode", "کد پرسنلی وارد شده قبلاً ثبت شده است.");
                await LoadSelectLists();
                return Page();
            }

            try
            {
                await _employeeService.UpdateEmployeeAsync(Employee);
                TempData["SuccessMessage"] = "اطلاعات کارمند با موفقیت به‌روزرسانی شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                await LoadSelectLists();
                return Page();
            }
        }

        private async Task LoadSelectLists()
        {
            var buildings = await _buildingService.GetAllBuildingsAsync();
            var jobs = await _jobService.GetAllJobsAsync();

            Buildings = new SelectList(buildings, "BuildingId", "Name");
            Jobs = new SelectList(jobs, "JobId", "Title");
        }
    }
}
