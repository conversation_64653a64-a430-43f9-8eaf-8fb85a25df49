﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class SeedData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert Buildings
            migrationBuilder.InsertData(
                table: "Buildings",
                columns: new[] { "Name", "Description" },
                values: new object[,]
                {
                    { "ساختمان مرکزی", "ساختمان اصلی شرکت واقع در تهران، خیابان ولیعصر" },
                    { "ساختمان شعبه شمال", "شعبه شمالی شرکت واقع در تهران، خیابان شریعتی" },
                    { "ساختمان شعبه غرب", "شعبه غربی شرکت واقع در تهران، خیابان ستاری" },
                    { "انبار مرکزی", "انبار اصلی شرکت واقع در جاده مخصوص کرج" }
                });

            // Insert Jobs
            migrationBuilder.InsertData(
                table: "Jobs",
                columns: new[] { "Title", "IsDriver" },
                values: new object[,]
                {
                    { "مدیر عامل", false },
                    { "مدیر فنی", false },
                    { "مهندس نرم‌افزار", false },
                    { "مهندس شبکه", false },
                    { "راننده", true },
                    { "نگهبان", false },
                    { "منشی", false },
                    { "حسابدار", false },
                    { "کارگر", false },
                    { "تکنسین", false }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove seed data
            migrationBuilder.DeleteData(
                table: "Jobs",
                keyColumn: "Title",
                keyValues: new object[]
                {
                    "مدیر عامل", "مدیر فنی", "مهندس نرم‌افزار", "مهندس شبکه",
                    "راننده", "نگهبان", "منشی", "حسابدار", "کارگر", "تکنسین"
                });

            migrationBuilder.DeleteData(
                table: "Buildings",
                keyColumn: "Name",
                keyValues: new object[]
                {
                    "ساختمان مرکزی", "ساختمان شعبه شمال", "ساختمان شعبه غرب", "انبار مرکزی"
                });
        }
    }
}
