using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Dashboard
{
    public class IndexModel : PageModel
    {
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly DailyAttendanceService _attendanceService;
        private readonly SystemSettingsService _systemSettingsService;
        private readonly ApplicationDbContext _context;

        public IndexModel(
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService,
            DailyAttendanceService attendanceService,
            SystemSettingsService systemSettingsService,
            ApplicationDbContext context)
        {
            _authService = authService;
            _employeeStatusService = employeeStatusService;
            _attendanceService = attendanceService;
            _systemSettingsService = systemSettingsService;
            _context = context;
        }

        public User CurrentUser { get; set; } = default!;
        public List<EmployeeStatus> PresentEmployees { get; set; } = new();
        public List<EmployeeStatus> EmployeesOutOfBuilding { get; set; } = new();
        public List<CarTrafficLog> CarsInParking { get; set; } = new();
        public List<CarTrafficLog> CarsOutOfParking { get; set; } = new();
        public AttendanceStats AttendanceStats { get; set; } = new();
        public SystemSettings SystemSettings { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync()
        {
            // تنظیم headers برای جلوگیری از cache
            Response.Headers.Add("Cache-Control", "no-cache, no-store, must-revalidate");
            Response.Headers.Add("Pragma", "no-cache");
            Response.Headers.Add("Expires", "0");

            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            CurrentUser = session.User;

            // دریافت شناسه ساختمان کاربر (اگر محدود باشد)
            int? userBuildingId = null;
            if (CurrentUser.BuildingId.HasValue)
            {
                userBuildingId = CurrentUser.BuildingId.Value;
            }

            try
            {
                // بارگذاری داده‌ها
                await LoadDashboardDataAsync(userBuildingId);
                
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات داشبورد: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDashboardDataAsync(int? buildingId)
        {
            // دریافت تنظیمات سیستم
            SystemSettings = await _systemSettingsService.GetSystemSettingsAsync();

            // دریافت کارمندان حاضر
            PresentEmployees = await _employeeStatusService.GetPresentEmployeesAsync(buildingId);

            // دریافت کارمندان خارج از ساختمان
            EmployeesOutOfBuilding = await _employeeStatusService.GetEmployeesOutOfBuildingAsync(buildingId);

            // دریافت آمار حضور
            AttendanceStats = await _attendanceService.GetAttendanceStatsAsync(DateTime.Today, buildingId);

            // دریافت خودروهای داخل پارکینگ
            await LoadCarDataAsync(buildingId);
        }

        private async Task LoadCarDataAsync(int? buildingId)
        {
            var carsQuery = _context.CarTrafficLogs
                                   .Include(c => c.DriverEmployee)
                                   .Include(c => c.Building)
                                   .AsQueryable();

            // فیلتر بر اساس ساختمان
            if (buildingId.HasValue)
            {
                carsQuery = carsQuery.Where(c => c.BuildingId == buildingId.Value);
            }

            // دریافت خودروهای داخل پارکینگ
            CarsInParking = await carsQuery
                                 .Where(c => c.Status == CarStatus.InsideParking)
                                 .OrderByDescending(c => c.EntryTime)
                                 .ToListAsync();

            // دریافت خودروهای خارج از پارکینگ (آخرین خروجی‌ها)
            CarsOutOfParking = await carsQuery
                                    .Where(c => c.Status == CarStatus.OutsideParking && c.ExitTime.HasValue)
                                    .OrderByDescending(c => c.ExitTime)
                                    .Take(20) // فقط 20 خودرو آخر
                                    .ToListAsync();
        }

        public string GetUserRoleDisplay()
        {
            return CurrentUser.Role switch
            {
                "Admin" => "مدیر سیستم",
                "Manager" => "مدیر ساختمان",
                "Guard" => "نگهبان",
                "User" => "کاربر",
                _ => CurrentUser.Role
            };
        }

        public bool CanAccessAdminFeatures()
        {
            return CurrentUser.Role == "Admin";
        }

        public bool CanAccessManagerFeatures()
        {
            return CurrentUser.Role == "Admin" || CurrentUser.Role == "Manager";
        }

        public bool CanRegisterTraffic()
        {
            return CurrentUser.Role == "Admin" || CurrentUser.Role == "Manager" || CurrentUser.Role == "Guard";
        }

        public string GetWelcomeMessage()
        {
            var hour = DateTime.Now.Hour;
            var greeting = hour switch
            {
                >= 5 and < 12 => "صبح بخیر",
                >= 12 and < 17 => "ظهر بخیر",
                >= 17 and < 20 => "عصر بخیر",
                _ => "شب بخیر"
            };

            return $"{greeting}، {CurrentUser.Username}";
        }

        public async Task<IActionResult> OnPostLogoutAsync()
        {
            var sessionToken = Request.Cookies["SessionToken"];
            if (!string.IsNullOrEmpty(sessionToken))
            {
                await _authService.LogoutAsync(sessionToken);
            }

            Response.Cookies.Delete("SessionToken");
            HttpContext.Session.Clear();

            TempData["InfoMessage"] = "شما با موفقیت از سیستم خارج شدید.";
            return RedirectToPage("/Account/Login");
        }
    }
}
