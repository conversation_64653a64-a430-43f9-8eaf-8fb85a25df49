@page "/test/add-sample-data"
@model EmployeeTrafficControl.Web.Pages.Test.AddSampleDataModel
@{
    ViewData["Title"] = "اضافه کردن داده‌های نمونه";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-database-add"></i> اضافه کردن داده‌های نمونه
                    </h4>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.Message))
                    {
                        <div class="alert alert-info">
                            @Model.Message
                        </div>
                    }

                    <form method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" asp-page-handler="AddSampleCar" class="btn btn-primary mb-3">
                                    <i class="bi bi-car-front"></i> اضافه کردن خودرو نمونه
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" asp-page-handler="AddSampleCarTraffic" class="btn btn-success mb-3">
                                    <i class="bi bi-arrow-right-circle"></i> اضافه کردن تردد خودرو نمونه
                                </button>
                            </div>
                        </div>
                    </form>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>خودروهای موجود:</h5>
                            @if (Model.Cars.Any())
                            {
                                <ul class="list-group">
                                    @foreach (var car in Model.Cars)
                                    {
                                        <li class="list-group-item">
                                            <strong>@car.PlateNumber</strong> - @car.Model (@car.Building.Name)
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted">هیچ خودرویی ثبت نشده است.</p>
                            }
                        </div>
                        <div class="col-md-6">
                            <h5>تردد خودروها:</h5>
                            @if (Model.CarTrafficLogs.Any())
                            {
                                <ul class="list-group">
                                    @foreach (var log in Model.CarTrafficLogs)
                                    {
                                        <li class="list-group-item">
                                            <strong>@log.PlateNumber</strong> - @log.CurrentStatus
                                            <br><small>@log.EntryTime?.ToString("yyyy/MM/dd HH:mm")</small>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted">هیچ تردد خودرویی ثبت نشده است.</p>
                            }
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="/dashboard" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
