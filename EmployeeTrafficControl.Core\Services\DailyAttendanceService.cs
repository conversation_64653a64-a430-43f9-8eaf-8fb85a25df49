using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;

namespace EmployeeTrafficControl.Services
{
    public class DailyAttendanceService
    {
        private readonly ApplicationDbContext _context;
        private readonly SystemSettingsService _systemSettingsService;

        public DailyAttendanceService(ApplicationDbContext context, SystemSettingsService systemSettingsService)
        {
            _context = context;
            _systemSettingsService = systemSettingsService;
        }

        /// <summary>
        /// دریافت حضور روزانه کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>حضور روزانه کارمند</returns>
        public async Task<DailyAttendance?> GetDailyAttendanceAsync(int employeeId, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            return await _context.DailyAttendances
                                 .Include(da => da.Employee)
                                 .Include(da => da.RegisteredByUser)
                                 .Include(da => da.ApprovedByUser)
                                 .FirstOrDefaultAsync(da => da.EmployeeId == employeeId && da.Date.Date == targetDate.Date);
        }

        /// <summary>
        /// ثبت ورود کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="checkInTime">زمان ورود (پیش‌فرض الان)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterCheckInAsync(int employeeId, int userId, DateTime? checkInTime = null)
        {
            try
            {
                var actualCheckInTime = checkInTime ?? DateTime.Now;
                var today = actualCheckInTime.Date;
                
                var attendance = await GetDailyAttendanceAsync(employeeId, today);
                
                if (attendance == null)
                {
                    // ایجاد رکورد جدید
                    attendance = new DailyAttendance
                    {
                        EmployeeId = employeeId,
                        Date = today,
                        CheckInTime = actualCheckInTime,
                        IsPresent = true,
                        RegisteredByUserId = userId,
                        RegisteredTime = DateTime.Now
                    };

                    // محاسبه تأخیر
                    var workStartTime = await _systemSettingsService.GetWorkStartTimeAsync();
                    attendance.CalculateLateMinutes(workStartTime);

                    _context.DailyAttendances.Add(attendance);
                }
                else
                {
                    // به‌روزرسانی رکورد موجود
                    if (!attendance.CheckInTime.HasValue)
                    {
                        attendance.CheckInTime = actualCheckInTime;
                        attendance.IsPresent = true;
                        
                        // محاسبه تأخیر
                        var workStartTime = await _systemSettingsService.GetWorkStartTimeAsync();
                        attendance.CalculateLateMinutes(workStartTime);
                        
                        attendance.LastUpdated = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering check-in: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ثبت خروج کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="checkOutTime">زمان خروج (پیش‌فرض الان)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterCheckOutAsync(int employeeId, int userId, DateTime? checkOutTime = null)
        {
            try
            {
                var actualCheckOutTime = checkOutTime ?? DateTime.Now;
                var today = actualCheckOutTime.Date;
                
                var attendance = await GetDailyAttendanceAsync(employeeId, today);
                
                if (attendance == null)
                {
                    // اگر ورود ثبت نشده، ابتدا ورود را ثبت می‌کنیم
                    var workStartTime = await _systemSettingsService.GetWorkStartTimeAsync();
                    var estimatedCheckIn = today.Add(workStartTime);
                    
                    attendance = new DailyAttendance
                    {
                        EmployeeId = employeeId,
                        Date = today,
                        CheckInTime = estimatedCheckIn,
                        CheckOutTime = actualCheckOutTime,
                        IsPresent = true,
                        RegisteredByUserId = userId,
                        RegisteredTime = DateTime.Now,
                        Notes = "ورود تخمینی - خروج ثبت شده"
                    };

                    // محاسبه ساعات کار و زودتر رفتن
                    attendance.CalculateWorkHours();
                    var workEndTime = await _systemSettingsService.GetWorkEndTimeAsync();
                    attendance.CalculateEarlyLeaveMinutes(workEndTime);

                    _context.DailyAttendances.Add(attendance);
                }
                else
                {
                    // به‌روزرسانی رکورد موجود
                    attendance.CheckOutTime = actualCheckOutTime;
                    
                    // محاسبه ساعات کار و زودتر رفتن
                    attendance.CalculateWorkHours();
                    var workEndTime = await _systemSettingsService.GetWorkEndTimeAsync();
                    attendance.CalculateEarlyLeaveMinutes(workEndTime);
                    
                    attendance.LastUpdated = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering check-out: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اضافه کردن زمان خروج ساعتی
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="exitDuration">مدت زمان خروج</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> AddHourlyExitTimeAsync(int employeeId, TimeSpan exitDuration)
        {
            try
            {
                var today = DateTime.Today;
                var attendance = await GetDailyAttendanceAsync(employeeId, today);
                
                if (attendance != null)
                {
                    attendance.TotalHourlyExitTime = attendance.TotalHourlyExitTime.Add(exitDuration);
                    attendance.HourlyExitCount++;
                    attendance.CalculateWorkHours(); // محاسبه مجدد ساعات کار
                    attendance.LastUpdated = DateTime.Now;
                    
                    await _context.SaveChangesAsync();
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error adding hourly exit time: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// دریافت گزارش حضور روزانه
        /// </summary>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <returns>لیست حضور روزانه</returns>
        public async Task<List<DailyAttendance>> GetDailyAttendanceReportAsync(DateTime? date = null, int? buildingId = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.DailyAttendances
                               .Include(da => da.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(da => da.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(da => da.RegisteredByUser)
                               .Where(da => da.Date.Date == targetDate.Date);

            if (buildingId.HasValue)
            {
                query = query.Where(da => da.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(da => da.Employee.Building != null ? da.Employee.Building.Name : "")
                             .ThenBy(da => da.Employee.FirstName)
                             .ThenBy(da => da.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// دریافت آمار حضور
        /// </summary>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <returns>آمار حضور</returns>
        public async Task<AttendanceStats> GetAttendanceStatsAsync(DateTime? date = null, int? buildingId = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.DailyAttendances
                               .Include(da => da.Employee)
                               .Where(da => da.Date.Date == targetDate.Date);

            if (buildingId.HasValue)
            {
                query = query.Where(da => da.Employee.BuildingId == buildingId.Value);
            }

            var attendances = await query.ToListAsync();
            
            return new AttendanceStats
            {
                TotalEmployees = attendances.Count,
                PresentEmployees = attendances.Count(a => a.IsPresent),
                AbsentEmployees = attendances.Count(a => !a.IsPresent),
                LateEmployees = attendances.Count(a => a.LateMinutes > 0),
                EarlyLeaveEmployees = attendances.Count(a => a.EarlyLeaveMinutes > 0),
                AverageLateMinutes = attendances.Where(a => a.LateMinutes > 0).Average(a => (double?)a.LateMinutes) ?? 0,
                AverageEarlyLeaveMinutes = attendances.Where(a => a.EarlyLeaveMinutes > 0).Average(a => (double?)a.EarlyLeaveMinutes) ?? 0
            };
        }

        /// <summary>
        /// تأیید حضور توسط مدیر
        /// </summary>
        /// <param name="attendanceId">شناسه حضور</param>
        /// <param name="approvedByUserId">شناسه کاربر تأیید‌کننده</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> ApproveAttendanceAsync(int attendanceId, int approvedByUserId)
        {
            try
            {
                var attendance = await _context.DailyAttendances.FindAsync(attendanceId);
                if (attendance != null)
                {
                    attendance.IsApproved = true;
                    attendance.ApprovedByUserId = approvedByUserId;
                    attendance.ApprovalTime = DateTime.Now;
                    attendance.LastUpdated = DateTime.Now;
                    
                    await _context.SaveChangesAsync();
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error approving attendance: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ثبت حضور دستی
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ</param>
        /// <param name="checkInTime">زمان ورود</param>
        /// <param name="checkOutTime">زمان خروج</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="notes">توضیحات</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterManualAttendanceAsync(int employeeId, DateTime date, 
            DateTime? checkInTime, DateTime? checkOutTime, int userId, string? notes = null)
        {
            try
            {
                var existingAttendance = await GetDailyAttendanceAsync(employeeId, date);
                
                if (existingAttendance != null)
                {
                    // به‌روزرسانی رکورد موجود
                    existingAttendance.CheckInTime = checkInTime;
                    existingAttendance.CheckOutTime = checkOutTime;
                    existingAttendance.IsPresent = checkInTime.HasValue;
                    existingAttendance.Notes = notes;
                    existingAttendance.LastUpdated = DateTime.Now;
                }
                else
                {
                    // ایجاد رکورد جدید
                    existingAttendance = new DailyAttendance
                    {
                        EmployeeId = employeeId,
                        Date = date,
                        CheckInTime = checkInTime,
                        CheckOutTime = checkOutTime,
                        IsPresent = checkInTime.HasValue,
                        Notes = notes,
                        RegisteredByUserId = userId,
                        RegisteredTime = DateTime.Now
                    };
                    
                    _context.DailyAttendances.Add(existingAttendance);
                }

                // محاسبه تأخیر و زودتر رفتن
                if (checkInTime.HasValue)
                {
                    var workStartTime = await _systemSettingsService.GetWorkStartTimeAsync();
                    existingAttendance.CalculateLateMinutes(workStartTime);
                }

                if (checkOutTime.HasValue)
                {
                    var workEndTime = await _systemSettingsService.GetWorkEndTimeAsync();
                    existingAttendance.CalculateEarlyLeaveMinutes(workEndTime);
                }

                existingAttendance.CalculateWorkHours();
                
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering manual attendance: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// کلاس آمار حضور
    /// </summary>
    public class AttendanceStats
    {
        public int TotalEmployees { get; set; }
        public int PresentEmployees { get; set; }
        public int AbsentEmployees { get; set; }
        public int LateEmployees { get; set; }
        public int EarlyLeaveEmployees { get; set; }
        public double AverageLateMinutes { get; set; }
        public double AverageEarlyLeaveMinutes { get; set; }
        
        public double AttendanceRate => TotalEmployees > 0 ? (double)PresentEmployees / TotalEmployees * 100 : 0;
        public double LateRate => PresentEmployees > 0 ? (double)LateEmployees / PresentEmployees * 100 : 0;
        public double EarlyLeaveRate => PresentEmployees > 0 ? (double)EarlyLeaveEmployees / PresentEmployees * 100 : 0;
    }
}
