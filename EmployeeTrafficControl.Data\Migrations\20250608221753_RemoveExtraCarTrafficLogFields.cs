﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    /// <inheritdoc />
    public partial class RemoveExtraCarTrafficLogFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars");

            migrationBuilder.AddForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars",
                column: "BuildingId",
                principalTable: "Buildings",
                principalColumn: "BuildingId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars");

            migrationBuilder.AddForeignKey(
                name: "FK_Cars_Buildings_BuildingId",
                table: "Cars",
                column: "BuildingId",
                principalTable: "Buildings",
                principalColumn: "BuildingId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
