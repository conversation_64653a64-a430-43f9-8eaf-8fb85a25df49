using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class CarExitNewModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;

        public CarExitNewModel(ApplicationDbContext context, AuthenticationService authService, EmployeeStatusService employeeStatusService)
        {
            _context = context;
            _authService = authService;
            _employeeStatusService = employeeStatusService;
        }

        public List<Car> AvailableCars { get; set; } = new();
        public List<EmployeeStatus> AvailableDrivers { get; set; } = new();
        public Car? SelectedCar { get; set; }
        public int? SelectedCarId { get; set; }
        public bool IsFirstExitToday { get; set; }
        public string Message { get; set; } = "";

        public async Task<IActionResult> OnGetAsync()
        {
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session?.User == null)
            {
                return RedirectToPage("/Account/Login");
            }

            await LoadAvailableCarsAsync(session.User.BuildingId);
            return Page();
        }

        public async Task<IActionResult> OnPostSelectCarAsync(int carId)
        {
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session?.User == null)
            {
                return RedirectToPage("/Account/Login");
            }

            await LoadAvailableCarsAsync(session.User.BuildingId);
            
            SelectedCarId = carId;
            SelectedCar = AvailableCars.FirstOrDefault(c => c.CarId == carId);
            
            if (SelectedCar != null)
            {
                await LoadAvailableDriversAsync(session.User.BuildingId);
                await CheckFirstExitTodayAsync(carId);
            }

            return Page();
        }

        public async Task<IActionResult> OnPostRegisterExitAsync(int carId, int driverId, string exitType, int? currentKilometer, List<int> passengerIds, string? notes)
        {
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session?.User == null)
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // بارگذاری خودرو
                var car = await _context.Cars
                    .Include(c => c.Building)
                    .FirstOrDefaultAsync(c => c.CarId == carId);

                if (car == null)
                {
                    Message = "خودرو یافت نشد.";
                    await LoadAvailableCarsAsync(session.User.BuildingId);
                    return Page();
                }

                // بررسی راننده
                var driver = await _context.Employees
                    .FirstOrDefaultAsync(e => e.EmployeeId == driverId && e.HasDrivingLicense);

                if (driver == null)
                {
                    Message = "راننده انتخاب شده معتبر نیست.";
                    await LoadDataForSelectedCar(carId, session.User.BuildingId);
                    return Page();
                }

                // ثبت کیلومتر (اگر لازم باشد)
                if (car.IsMoneyTransport && currentKilometer.HasValue)
                {
                    var existingKilometer = await _context.CarKilometers
                        .FirstOrDefaultAsync(ck => ck.CarId == carId && ck.Date.Date == DateTime.Today);

                    if (existingKilometer == null)
                    {
                        var carKilometer = new CarKilometer
                        {
                            CarId = carId,
                            Date = DateTime.Today,
                            StartKilometer = currentKilometer.Value,
                            CreatedByUserId = session.UserId,
                            Notes = "ثبت خودکار در اولین خروج روز"
                        };
                        _context.CarKilometers.Add(carKilometer);
                    }
                }

                // به‌روزرسانی وضعیت خودرو
                car.CurrentStatus = "خارج از پارکینگ";

                // ثبت تردد خودرو
                var carTrafficLog = new CarTrafficLog
                {
                    CarId = carId,
                    DriverEmployeeId = driverId,
                    BuildingId = car.BuildingId,
                    EntryTime = DateTime.Now, // زمان خروج از پارکینگ
                    CurrentStatus = "خارج از پارکینگ",
                    ExitType = exitType,
                    Notes = notes
                };

                _context.CarTrafficLogs.Add(carTrafficLog);
                await _context.SaveChangesAsync();

                // ثبت سرنشینان
                if (passengerIds?.Any() == true)
                {
                    foreach (var passengerId in passengerIds)
                    {
                        var passenger = new CarPassenger
                        {
                            CarTrafficLogId = carTrafficLog.CarTrafficLogId,
                            EmployeeId = passengerId,
                            IsEntered = false // هنوز برنگشته‌اند
                        };
                        _context.CarPassengers.Add(passenger);
                    }
                }

                // ثبت خروج برای راننده
                await _employeeStatusService.RegisterEmployeeExitAsync(driverId, session.UserId);

                // ثبت خروج برای سرنشینان
                if (passengerIds?.Any() == true)
                {
                    foreach (var passengerId in passengerIds)
                    {
                        await _employeeStatusService.RegisterEmployeeExitAsync(passengerId, session.UserId);
                    }
                }

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"خروج خودرو {car.PlateNumber} با موفقیت ثبت شد.";
                return RedirectToPage("/Dashboard/Index");
            }
            catch (Exception ex)
            {
                Message = $"خطا در ثبت خروج: {ex.Message}";
                await LoadDataForSelectedCar(carId, session.User.BuildingId);
                return Page();
            }
        }

        private async Task LoadAvailableCarsAsync(int? buildingId)
        {
            AvailableCars = await _context.Cars
                .Include(c => c.Building)
                .Where(c => c.BuildingId == buildingId && c.CurrentStatus == "در پارکینگ")
                .OrderBy(c => c.PlateNumber)
                .ToListAsync();
        }

        private async Task LoadAvailableDriversAsync(int? buildingId)
        {
            AvailableDrivers = await _employeeStatusService.GetAvailableDriversAsync(buildingId);
        }

        private async Task CheckFirstExitTodayAsync(int carId)
        {
            var todayExits = await _context.CarTrafficLogs
                .Where(ct => ct.CarId == carId && 
                           ct.EntryTime.HasValue && 
                           ct.EntryTime.Value.Date == DateTime.Today)
                .CountAsync();

            IsFirstExitToday = todayExits == 0;
        }

        private async Task LoadDataForSelectedCar(int carId, int? buildingId)
        {
            await LoadAvailableCarsAsync(buildingId);
            SelectedCarId = carId;
            SelectedCar = AvailableCars.FirstOrDefault(c => c.CarId == carId);
            
            if (SelectedCar != null)
            {
                await LoadAvailableDriversAsync(buildingId);
                await CheckFirstExitTodayAsync(carId);
            }
        }
    }
}
