@page "/traffic/car-entry"
@model EmployeeTrafficControl.Web.Pages.Traffic.CarEntryModel
@{
    ViewData["Title"] = "ورود خودرو";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-car-front"></i> ورود خودرو</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">ثبت ورود خودروهای کارمندان و مهمانان</p>
</div>

<!-- فرم ثبت ورود سریع -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-plus-circle"></i> ثبت ورود سریع خودرو
                </h5>
            </div>
            <div class="card-body">
                <form id="quickEntryForm">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">شماره پلاک <span class="text-danger">*</span></label>
                            <input type="text" id="plateNumber" class="form-control" placeholder="مثال: 12ج345-23" required>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع خودرو</label>
                            <select id="carType" class="form-select">
                                <option value="employee">خودرو کارمند</option>
                                <option value="guest">خودرو مهمان</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="driverSection">
                            <label class="form-label">راننده</label>
                            <select id="driverId" class="form-select">
                                <option value="">انتخاب راننده...</option>
                                @foreach (var driver in Model.Drivers)
                                {
                                    <option value="@driver.EmployeeId">@driver.FirstName @driver.LastName (@driver.PersonnelCode)</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3" id="guestSection" style="display: none;">
                            <label class="form-label">نام مهمان</label>
                            <input type="text" id="guestName" class="form-control" placeholder="نام مهمان">
                        </div>
                        <div class="col-md-12">
                            <label class="form-label">توضیحات</label>
                            <input type="text" id="notes" class="form-control" placeholder="توضیحات اختیاری">
                        </div>
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle"></i> ثبت ورود خودرو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">جستجو</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="شماره پلاک، راننده یا مهمان" />
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">نوع خودرو</label>
                        <select name="CarType" class="form-select">
                            <option value="">همه انواع</option>
                            <option value="employee" selected="@(Model.CarType == "employee")">خودرو کارمند</option>
                            <option value="guest" selected="@(Model.CarType == "guest")">خودرو مهمان</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">وضعیت</label>
                        <select name="Status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="inside" selected="@(Model.Status == "inside")">داخل پارکینگ</option>
                            <option value="outside" selected="@(Model.Status == "outside")">خارج از پارکینگ</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-total">
            <div class="stats-icon">
                <i class="bi bi-car-front"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TotalCarsInside</h3>
                <p>کل خودروها داخل</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-employee">
            <div class="stats-icon">
                <i class="bi bi-person-badge"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeeCarsInside</h3>
                <p>خودرو کارمندان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-guest">
            <div class="stats-icon">
                <i class="bi bi-person-plus"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.GuestCarsInside</h3>
                <p>خودرو مهمانان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-today">
            <div class="stats-icon">
                <i class="bi bi-calendar-day"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.TodayEntries</h3>
                <p>ورود امروز</p>
            </div>
        </div>
    </div>
</div>

<!-- لیست خودروهای داخل پارکینگ -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> خودروهای داخل پارکینگ
                </h5>
                <span class="badge bg-success">@Model.CarsInside.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.CarsInside.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>شماره پلاک</th>
                                    <th>نوع</th>
                                    <th>راننده/مهمان</th>
                                    <th>ساختمان</th>
                                    <th>زمان ورود</th>
                                    <th>مدت حضور</th>
                                    <th>توضیحات</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var car in Model.CarsInside)
                                {
                                    <tr>
                                        <td>
                                            <strong class="text-primary">@car.PlateNumber</strong>
                                        </td>
                                        <td>
                                            @if (car.DriverEmployeeId != null)
                                            {
                                                <span class="badge bg-info">خودرو کارمند</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">خودرو مهمان</span>
                                            }
                                        </td>
                                        <td>
                                            @if (car.DriverEmployee != null)
                                            {
                                                <div>
                                                    <strong>@car.DriverEmployee.FirstName @car.DriverEmployee.LastName</strong>
                                                    <br><small class="text-muted">@car.DriverEmployee.PersonnelCode</small>
                                                </div>
                                            }
                                            else if (!string.IsNullOrEmpty(car.GuestName))
                                            {
                                                <span class="text-warning">@car.GuestName</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">نامشخص</span>
                                            }
                                        </td>
                                        <td>
                                            @if (car.Building != null)
                                            {
                                                <span class="badge bg-secondary">@car.Building.Name</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="text-success">@(car.EntryTime?.ToString("HH:mm") ?? "-")</span>
                                            <br><small class="text-muted">@(car.EntryTime?.ToString("yyyy/MM/dd") ?? "-")</small>
                                        </td>
                                        <td>
                                            @{
                                                var duration = car.EntryTime.HasValue ? DateTime.Now - car.EntryTime.Value : TimeSpan.Zero;
                                                var hours = (int)duration.TotalHours;
                                                var minutes = duration.Minutes;
                                            }
                                            <span class="text-info">@hours:@minutes.ToString("D2")</span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(car.Notes))
                                            {
                                                <small class="text-muted">@car.Notes</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="registerCarExit(@car.CarTrafficLogId, '@car.PlateNumber')">
                                                <i class="bi bi-box-arrow-right"></i> ثبت خروج
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-car-front-fill" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">هیچ خودرویی در پارکینگ نیست</h5>
                        <p>همه خودروها خارج از پارکینگ هستند.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-total {
        border-right: 4px solid #6c757d;
    }
    .stats-total .stats-icon { color: #6c757d; }

    .stats-employee {
        border-right: 4px solid #007bff;
    }
    .stats-employee .stats-icon { color: #007bff; }

    .stats-guest {
        border-right: 4px solid #ffc107;
    }
    .stats-guest .stats-icon { color: #ffc107; }

    .stats-today {
        border-right: 4px solid #28a745;
    }
    .stats-today .stats-icon { color: #28a745; }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }
</style>

<script>
    // تغییر نوع خودرو
    document.getElementById('carType').addEventListener('change', function() {
        const carType = this.value;
        const driverSection = document.getElementById('driverSection');
        const guestSection = document.getElementById('guestSection');

        if (carType === 'employee') {
            driverSection.style.display = 'block';
            guestSection.style.display = 'none';
            document.getElementById('guestName').required = false;
        } else {
            driverSection.style.display = 'none';
            guestSection.style.display = 'block';
            document.getElementById('guestName').required = true;
        }
    });

    // ثبت ورود سریع
    document.getElementById('quickEntryForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const plateNumber = document.getElementById('plateNumber').value.trim();
        const carType = document.getElementById('carType').value;
        const driverId = document.getElementById('driverId').value;
        const guestName = document.getElementById('guestName').value.trim();
        const notes = document.getElementById('notes').value.trim();

        if (!plateNumber) {
            alert('لطفاً شماره پلاک را وارد کنید.');
            return;
        }

        if (carType === 'employee' && !driverId) {
            alert('لطفاً راننده را انتخاب کنید.');
            return;
        }

        if (carType === 'guest' && !guestName) {
            alert('لطفاً نام مهمان را وارد کنید.');
            return;
        }

        try {
            const response = await fetch('/api/traffic/register-car-entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    plateNumber: plateNumber,
                    carType: carType,
                    driverId: carType === 'employee' ? parseInt(driverId) : null,
                    guestName: carType === 'guest' ? guestName : null,
                    notes: notes || null
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'ورود خودرو با موفقیت ثبت شد');

                // پاک کردن فرم
                document.getElementById('quickEntryForm').reset();
                document.getElementById('carType').dispatchEvent(new Event('change'));

                // برگشت به داشبورد
                window.location.href = '/dashboard';
            } else {
                alert(result.message || 'خطا در ثبت ورود خودرو. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
        }
    });

    async function registerCarExit(carTrafficLogId, plateNumber) {
        if (!confirm(`آیا مطمئن هستید که می‌خواهید خروج خودرو "${plateNumber}" را ثبت کنید؟`)) {
            return;
        }

        try {
            const response = await fetch('/api/traffic/register-car-exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    carTrafficLogId: carTrafficLogId
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'خروج خودرو با موفقیت ثبت شد');
                window.location.href = '/dashboard';
            } else {
                alert(result.message || 'خطا در ثبت خروج خودرو. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
        }
    }

    // تنظیم اولیه
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('carType').dispatchEvent(new Event('change'));
    });
</script>
