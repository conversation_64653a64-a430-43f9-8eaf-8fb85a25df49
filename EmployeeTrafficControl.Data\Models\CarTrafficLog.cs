﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Models
{
    public class CarTrafficLog
    {
        [Key]
        [Display(Name = "شناسه تردد خودرو")]
        public int CarTrafficLogId { get; set; }

        [Display(Name = "خودرو")]
        public int? CarId { get; set; }

        [Required(ErrorMessage = "شماره پلاک اجباری است.")]
        [StringLength(20, ErrorMessage = "شماره پلاک حداکثر 20 کاراکتر باشد.")]
        [Display(Name = "شماره پلاک")]
        public string PlateNumber { get; set; } = "";

        [Display(Name = "راننده")]
        public int? DriverEmployeeId { get; set; }

        [StringLength(100, ErrorMessage = "نام مهمان حداکثر 100 کاراکتر باشد.")]
        [Display(Name = "نام مهمان")]
        public string? GuestName { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان ورود")]
        public DateTime EntryTime { get; set; } = DateTime.Now;

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان خروج")]
        public DateTime? ExitTime { get; set; }

        [Display(Name = "وضعیت فعلی")]
        public CarStatus Status { get; set; } = CarStatus.OutsideParking;

        // برای سازگاری با کد قبلی - این فیلد deprecated است
        [Required(ErrorMessage = "وضعیت فعلی خودرو اجباری است.")]
        [StringLength(50, ErrorMessage = "وضعیت فعلی خودرو حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "وضعیت فعلی (متنی)")]
        public string CurrentStatus
        {
            get => Status.ToDbString();
            set => Status = CarStatusExtensions.FromString(value);
        }

        [StringLength(4000, ErrorMessage = "توضیحات حداکثر 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Notes { get; set; }

        [Display(Name = "خودرو")]
        public Car? Car { get; set; }

        [Display(Name = "راننده")]
        public Employee? DriverEmployee { get; set; }

        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;

        public ICollection<CarPassenger> CarPassengers { get; set; } = new List<CarPassenger>();

        // متدهای کمکی
        /// <summary>
        /// بررسی اینکه آیا خودرو در پارکینگ است یا نه
        /// </summary>
        public bool IsInParking => Status.IsInside();

        /// <summary>
        /// بررسی اینکه آیا خودرو خارج از پارکینگ است یا نه
        /// </summary>
        public bool IsOutOfParking => Status.IsOutside();

        /// <summary>
        /// دریافت مدت زمان حضور در پارکینگ
        /// </summary>
        public TimeSpan? GetParkingDuration()
        {
            if (ExitTime.HasValue)
            {
                return ExitTime.Value - EntryTime;
            }
            else if (IsInParking)
            {
                return DateTime.Now - EntryTime;
            }
            return null;
        }

        /// <summary>
        /// دریافت نوع خودرو (کارمند یا مهمان)
        /// </summary>
        public string GetCarType()
        {
            return DriverEmployeeId.HasValue ? "کارمند" : "مهمان";
        }

        /// <summary>
        /// دریافت نام راننده یا مهمان
        /// </summary>
        public string GetDriverOrGuestName()
        {
            if (DriverEmployee != null)
            {
                return $"{DriverEmployee.FirstName} {DriverEmployee.LastName}";
            }
            else if (!string.IsNullOrEmpty(GuestName))
            {
                return GuestName;
            }
            return "نامشخص";
        }

        /// <summary>
        /// دریافت کلاس CSS برای badge وضعیت
        /// </summary>
        public string GetStatusBadgeClass()
        {
            return Status.GetStatusBadgeClass();
        }

        /// <summary>
        /// دریافت نام نمایشی وضعیت
        /// </summary>
        public string GetStatusDisplayName()
        {
            return Status.GetDisplayName();
        }

        /// <summary>
        /// دریافت آیکون وضعیت
        /// </summary>
        public string GetStatusIcon()
        {
            return Status.GetStatusIcon();
        }
    }
}