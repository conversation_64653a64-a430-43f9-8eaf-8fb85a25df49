﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Models
{
    public class CarTrafficLog
    {
        [Key]
        [Display(Name = "شناسه تردد خودرو")]
        public int CarTrafficLogId { get; set; }

        [Display(Name = "خودرو")]
        public int CarId { get; set; }

        [Display(Name = "راننده")]
        public int DriverEmployeeId { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان ورود")]
        public DateTime? EntryTime { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان خروج")]
        public DateTime? ExitTime { get; set; }

        [Required(ErrorMessage = "وضعیت فعلی خودرو اجباری است.")]
        [StringLength(50, ErrorMessage = "وضعیت فعلی خودرو حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "وضعیت فعلی")]
        public string CurrentStatus { get; set; } = "در پارکینگ";

        [StringLength(4000, ErrorMessage = "توضیحات حداکثر 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Notes { get; set; }

        // Navigation Properties
        [Display(Name = "خودرو")]
        public Car Car { get; set; } = default!;

        [Display(Name = "راننده")]
        public Employee DriverEmployee { get; set; } = default!;

        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;

        public ICollection<CarPassenger> CarPassengers { get; set; } = new List<CarPassenger>();

        // Properties for compatibility with new code
        public string PlateNumber
        {
            get => Car?.PlateNumber ?? "";
            set { /* Ignore setter for compatibility */ }
        }

        public string? GuestName
        {
            get => DriverEmployee == null ? "مهمان" : null;
            set { /* Ignore setter for compatibility */ }
        }

        public CarStatus Status
        {
            get => CurrentStatus == "در پارکینگ" ? CarStatus.InsideParking : CarStatus.OutsideParking;
            set => CurrentStatus = value == CarStatus.InsideParking ? "در پارکینگ" : "خارج از پارکینگ";
        }

        // Helper methods
        public bool IsInParking => Status.IsInside();
        public bool IsOutOfParking => Status.IsOutside();

        public TimeSpan? GetParkingDuration()
        {
            if (ExitTime.HasValue && EntryTime.HasValue)
            {
                return ExitTime.Value - EntryTime.Value;
            }
            else if (IsInParking && EntryTime.HasValue)
            {
                return DateTime.Now - EntryTime.Value;
            }
            return null;
        }

        public string GetCarType()
        {
            return "کارمند";
        }

        public string GetDriverOrGuestName()
        {
            if (DriverEmployee != null)
            {
                return $"{DriverEmployee.FirstName} {DriverEmployee.LastName}";
            }
            return "نامشخص";
        }

        public string GetStatusBadgeClass()
        {
            return Status.GetStatusBadgeClass();
        }

        public string GetStatusDisplayName()
        {
            return Status.GetDisplayName();
        }

        public string GetStatusIcon()
        {
            return Status.GetStatusIcon();
        }
    }
}