﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeTrafficControl.Models
{
    public class CarTrafficLog
    {
        [Key]
        [Display(Name = "شناسه تردد خودرو")]
        public int CarTrafficLogId { get; set; }

        [Display(Name = "خودرو")]
        public int? CarId { get; set; }

        [Required(ErrorMessage = "شماره پلاک اجباری است.")]
        [StringLength(20, ErrorMessage = "شماره پلاک حداکثر 20 کاراکتر باشد.")]
        [Display(Name = "شماره پلاک")]
        public string PlateNumber { get; set; } = "";

        [Display(Name = "راننده")]
        public int? DriverEmployeeId { get; set; }

        [StringLength(100, ErrorMessage = "نام مهمان حداکثر 100 کاراکتر باشد.")]
        [Display(Name = "نام مهمان")]
        public string? GuestName { get; set; }

        [Display(Name = "ساختمان")]
        public int BuildingId { get; set; }

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان ورود")]
        public DateTime EntryTime { get; set; } = DateTime.Now;

        [Column(TypeName = "datetime2")]
        [Display(Name = "زمان خروج")]
        public DateTime? ExitTime { get; set; }

        [Required(ErrorMessage = "وضعیت فعلی خودرو اجباری است.")]
        [StringLength(50, ErrorMessage = "وضعیت فعلی خودرو حداکثر 50 کاراکتر باشد.")]
        [Display(Name = "وضعیت فعلی")]
        public string CurrentStatus { get; set; }

        [StringLength(4000, ErrorMessage = "توضیحات حداکثر 4000 کاراکتر باشد.")]
        [Display(Name = "توضیحات")]
        public string? Notes { get; set; }

        [Display(Name = "خودرو")]
        public Car? Car { get; set; }

        [Display(Name = "راننده")]
        public Employee? DriverEmployee { get; set; }

        [Display(Name = "ساختمان")]
        public Building Building { get; set; } = default!;

        public ICollection<CarPassenger> CarPassengers { get; set; } = new List<CarPassenger>();
    }
}