@page "/traffic/final-exit"
@model EmployeeTrafficControl.Web.Pages.Traffic.FinalExitModel
@{
    ViewData["Title"] = "خروج نهایی (آخر وقت اداری)";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-door-open"></i> خروج نهایی (آخر وقت اداری)</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">ثبت خروج نهایی کارمندان در پایان روز کاری</p>
</div>

<!-- اطلاعات ساعت کاری -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="alert-heading mb-2">
                        <i class="bi bi-clock"></i> اطلاعات ساعت کاری امروز
                    </h6>
                    <p class="mb-0">
                        ساعت شروع کار: <strong>@Model.WorkStartTime.ToString(@"hh\:mm")</strong> |
                        ساعت پایان کار: <strong>@Model.WorkEndTime.ToString(@"hh\:mm")</strong> |
                        ساعت فعلی: <strong>@DateTime.Now.ToString("HH:mm")</strong>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    @if (DateTime.Now.TimeOfDay >= Model.WorkEndTime)
                    {
                        <span class="badge bg-success fs-6">وقت خروج رسیده</span>
                    }
                    else
                    {
                        var remainingTime = Model.WorkEndTime - DateTime.Now.TimeOfDay;
                        <span class="badge bg-warning fs-6">@((int)remainingTime.TotalMinutes) دقیقه تا پایان کار</span>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">جستجو در کارمندان</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="نام، نام خانوادگی یا کد پرسنلی" />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">شغل</label>
                        <select name="JobId" class="form-select" asp-items="Model.Jobs">
                            <option value="">همه مشاغل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-present">
            <div class="stats-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.PresentEmployees.Count</h3>
                <p>حاضر در ساختمان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-final-exit">
            <div class="stats-icon">
                <i class="bi bi-door-open"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.FinalExitCount</h3>
                <p>خروج نهایی شده</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-early">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EarlyExitCount</h3>
                <p>زودتر رفته‌اند</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-overtime">
            <div class="stats-icon">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.OvertimeCount</h3>
                <p>اضافه کار</p>
            </div>
        </div>
    </div>
</div>

<!-- عملیات گروهی -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> عملیات گروهی
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button type="button" class="btn btn-success w-100" onclick="selectAllEmployees()">
                            <i class="bi bi-check-all"></i> انتخاب همه کارمندان
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-warning w-100" onclick="selectOnTimeEmployees()">
                            <i class="bi bi-clock"></i> انتخاب کارمندان به موقع
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-danger w-100" onclick="registerSelectedFinalExits()" id="finalExitBtn" disabled>
                            <i class="bi bi-door-open"></i> ثبت خروج نهایی انتخاب شده‌ها
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- لیست کارمندان -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> کارمندان حاضر در ساختمان
                </h5>
                <span class="badge bg-success">@Model.PresentEmployees.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.PresentEmployees.Any())
                {
                    <form id="finalExitForm">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAll" class="form-check-input" onchange="toggleAllSelection()">
                                        </th>
                                        <th>کارمند</th>
                                        <th>کد پرسنلی</th>
                                        <th>ساختمان</th>
                                        <th>شغل</th>
                                        <th>زمان ورود</th>
                                        <th>ساعات کار</th>
                                        <th>وضعیت خروج</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var status in Model.PresentEmployees)
                                    {
                                        var attendance = Model.TodayAttendances.FirstOrDefault(a => a.EmployeeId == status.EmployeeId);
                                        var workHours = attendance?.TotalWorkHours;
                                        var isOnTime = DateTime.Now.TimeOfDay >= Model.WorkEndTime;
                                        var isEarly = DateTime.Now.TimeOfDay < Model.WorkEndTime;
                                        
                                        <tr class="employee-row" data-employee-id="@status.EmployeeId" data-on-time="@isOnTime.ToString().ToLower()">
                                            <td>
                                                <input type="checkbox" class="form-check-input employee-checkbox" 
                                                       value="@status.EmployeeId" onchange="updateFinalExitButton()">
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>@status.Employee.FirstName @status.Employee.LastName</strong>
                                                    @if (!string.IsNullOrEmpty(status.Employee.PhoneNumber))
                                                    {
                                                        <br><small class="text-muted">@status.Employee.PhoneNumber</small>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <code>@status.Employee.PersonnelCode</code>
                                            </td>
                                            <td>
                                                @if (status.Employee.Building != null)
                                                {
                                                    <span class="badge bg-info">@status.Employee.Building.Name</span>
                                                }
                                            </td>
                                            <td>
                                                @if (status.Employee.Job != null)
                                                {
                                                    <span class="badge bg-secondary">@status.Employee.Job.Title</span>
                                                    @if (status.Employee.Job.IsDriver)
                                                    {
                                                        <span class="badge bg-warning ms-1">راننده</span>
                                                    }
                                                }
                                            </td>
                                            <td>
                                                @if (attendance?.CheckInTime.HasValue == true)
                                                {
                                                    <span class="text-success">@attendance.CheckInTime.Value.ToString("HH:mm")</span>
                                                    @if (attendance.LateMinutes > 0)
                                                    {
                                                        <br><span class="badge bg-warning">@attendance.LateMinutes دقیقه تأخیر</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (workHours.HasValue)
                                                {
                                                    var hours = (int)workHours.Value.TotalHours;
                                                    var minutes = workHours.Value.Minutes;
                                                    <span class="text-info">@hours:@minutes.ToString("D2")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">محاسبه نشده</span>
                                                }
                                            </td>
                                            <td>
                                                @if (isOnTime)
                                                {
                                                    <span class="badge bg-success">به موقع</span>
                                                }
                                                else
                                                {
                                                    var earlyMinutes = (int)(Model.WorkEndTime - DateTime.Now.TimeOfDay).TotalMinutes;
                                                    <span class="badge bg-warning">@earlyMinutes دقیقه زودتر</span>
                                                }
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="registerSingleFinalExit(@status.EmployeeId)">
                                                    <i class="bi bi-door-open"></i> خروج نهایی
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </form>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">همه کارمندان خروج نهایی کرده‌اند</h5>
                        <p>هیچ کارمندی در ساختمان حضور ندارد.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-final-exit {
        border-right: 4px solid #dc3545;
    }
    .stats-final-exit .stats-icon { color: #dc3545; }

    .stats-early {
        border-right: 4px solid #ffc107;
    }
    .stats-early .stats-icon { color: #ffc107; }

    .stats-overtime {
        border-right: 4px solid #17a2b8;
    }
    .stats-overtime .stats-icon { color: #17a2b8; }

    .employee-row:hover {
        background-color: #f8f9fa;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }
</style>

<script>
    function toggleAllSelection() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.employee-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateFinalExitButton();
    }

    function selectAllEmployees() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        document.getElementById('selectAll').checked = true;
        updateFinalExitButton();
    }

    function selectOnTimeEmployees() {
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const isOnTime = row.getAttribute('data-on-time') === 'true';
            checkbox.checked = isOnTime;
        });
        updateFinalExitButton();
    }

    function updateFinalExitButton() {
        const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');
        const finalExitBtn = document.getElementById('finalExitBtn');

        if (checkedBoxes.length > 0) {
            finalExitBtn.disabled = false;
            finalExitBtn.innerHTML = `<i class="bi bi-door-open"></i> ثبت خروج نهایی ${checkedBoxes.length} نفر`;
        } else {
            finalExitBtn.disabled = true;
            finalExitBtn.innerHTML = '<i class="bi bi-door-open"></i> ثبت خروج نهایی انتخاب شده‌ها';
        }
    }

    async function registerSingleFinalExit(employeeId) {
        if (!confirm('آیا مطمئن هستید که می‌خواهید خروج نهایی این کارمند را ثبت کنید؟')) {
            return;
        }

        try {
            const response = await fetch('/api/traffic/register-exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeIds: [employeeId],
                    exitType: 'final'
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'خروج نهایی با موفقیت ثبت شد');
                location.reload();
            } else {
                alert(result.message || 'خطا در ثبت خروج نهایی. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
        }
    }

    async function registerSelectedFinalExits() {
        const checkedBoxes = document.querySelectorAll('.employee-checkbox:checked');
        const employeeIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));

        if (employeeIds.length === 0) {
            alert('لطفاً حداقل یک کارمند انتخاب کنید.');
            return;
        }

        if (!confirm(`آیا مطمئن هستید که می‌خواهید خروج نهایی ${employeeIds.length} کارمند را ثبت کنید؟`)) {
            return;
        }

        const finalExitBtn = document.getElementById('finalExitBtn');
        const originalText = finalExitBtn.innerHTML;
        finalExitBtn.disabled = true;
        finalExitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> در حال ثبت...';

        try {
            const response = await fetch('/api/traffic/register-exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeIds: employeeIds,
                    exitType: 'final'
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'خروج نهایی با موفقیت ثبت شد');
                location.reload();
            } else {
                alert(result.message || 'خطا در ثبت خروج نهایی. لطفاً دوباره تلاش کنید.');
                finalExitBtn.disabled = false;
                finalExitBtn.innerHTML = originalText;
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
            finalExitBtn.disabled = false;
            finalExitBtn.innerHTML = originalText;
        }
    }

    // تنظیم اولیه
    document.addEventListener('DOMContentLoaded', function() {
        updateFinalExitButton();
    });
</script>
