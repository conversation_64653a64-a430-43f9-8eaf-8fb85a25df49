@page "/dashboard"
@model EmployeeTrafficControl.Web.Pages.Dashboard.IndexModel
@{
    ViewData["Title"] = "داشبورد";
}

<div class="dashboard-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="dashboard-title">
                <i class="bi bi-speedometer2"></i>
                داشبورد سیستم کنترل تردد
            </h1>
            <p class="dashboard-subtitle">
                خوش آمدید، @Model.CurrentUser.Username
                <span class="badge bg-primary ms-2">@Model.GetUserRoleDisplay()</span>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <div class="dashboard-date">
                <i class="bi bi-calendar3"></i>
                @DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("fa-IR"))
                <br>
                <small class="text-muted">@DateTime.Now.ToString("HH:mm")</small>
            </div>
        </div>
    </div>
</div>

<!-- آمار کلی -->
<div class="row mb-4">
    <div class="col-md-3">
        <a href="/reports/present-employees" class="text-decoration-none">
            <div class="stats-card stats-present">
                <div class="stats-icon">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="stats-content">
                    <h3>@Model.AttendanceStats.PresentEmployees</h3>
                    <p>کارمندان حاضر</p>
                    <small>از @Model.AttendanceStats.TotalEmployees نفر</small>
                </div>
            </div>
        </a>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card stats-absent">
            <div class="stats-icon">
                <i class="bi bi-person-x"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.AttendanceStats.AbsentEmployees</h3>
                <p>کارمندان غایب</p>
                <small>@Model.AttendanceStats.AttendanceRate.ToString("F1")% حضور</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card stats-hourly-exit">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.EmployeesOutOfBuilding.Count</h3>
                <p>خروج ساعتی</p>
                <small>در حال حاضر</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card stats-late">
            <div class="stats-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.AttendanceStats.LateEmployees</h3>
                <p>تأخیر داشته‌اند</p>
                <small>@Model.AttendanceStats.LateRate.ToString("F1")% از حاضرین</small>
            </div>
        </div>
    </div>
</div>

<!-- عملیات سریع -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> عملیات سریع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="/traffic/daily-entry" class="quick-action-btn">
                            <i class="bi bi-sunrise"></i>
                            <span>ورود اولیه روزانه</span>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/traffic/individual-exit" class="quick-action-btn">
                            <i class="bi bi-clock-history"></i>
                            <span>مرخصی/ماموریت ساعتی</span>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/traffic/individual-entry" class="quick-action-btn">
                            <i class="bi bi-person-plus"></i>
                            <span>بازگشت از مرخصی/ماموریت</span>
                        </a>
                    </div>
                    
                    <div class="col-md-3">
                        <a href="/traffic/final-exit" class="quick-action-btn">
                            <i class="bi bi-sunset"></i>
                            <span>خروج نهایی روزانه</span>
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <a href="/traffic/car-exit" class="quick-action-btn">
                        <i class="bi bi-car-front-fill"></i>
                        <span>خروج خودرو</span>
                    </a>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-4">
                        <a href="/traffic/car-entry" class="quick-action-btn">
                            <i class="bi bi-car-front"></i>
                            <span>ورود خودرو</span>
                        </a>
                    </div>
                   
                    <div class="col-md-4">
                        <a href="/reports/daily-attendance" class="quick-action-btn">
                            <i class="bi bi-file-earmark-text"></i>
                            <span>گزارش حضور</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- کارمندان حاضر و خارج از ساختمان -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people"></i> کارمندان حاضر در ساختمان
                </h5>
                <span class="badge bg-success">@Model.PresentEmployees.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.PresentEmployees.Any())
                {
                    <div class="employee-list">
                        @foreach (var employee in Model.PresentEmployees.Take(10))
                        {
                            <div class="employee-item">
                                <div class="employee-info">
                                    <strong>@employee.Employee.FirstName @employee.Employee.LastName</strong>
                                    <small class="text-muted d-block">@employee.Employee.PersonnelCode</small>
                                </div>
                                <div class="employee-status">
                                    <span class="badge @employee.GetStatusBadgeClass()">
                                        @employee.GetStatusDisplayName()
                                    </span>
                                    @if (employee.EntryTime.HasValue)
                                    {
                                        <small class="text-muted d-block">ورود: @employee.EntryTime.Value.ToString("HH:mm")</small>
                                    }
                                </div>
                            </div>
                        }
                        @if (Model.PresentEmployees.Count > 10)
                        {
                            <div class="text-center mt-3">
                                <a href="/reports/present-employees" class="btn btn-sm btn-outline-primary">
                                    مشاهده همه (@Model.PresentEmployees.Count نفر)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-person-slash" style="font-size: 2rem;"></i>
                        <p class="mt-2">هیچ کارمندی در حال حاضر در ساختمان نیست</p>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> کارمندان خارج از ساختمان
                </h5>
                <span class="badge bg-warning">@Model.EmployeesOutOfBuilding.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.EmployeesOutOfBuilding.Any())
                {
                    <div class="employee-list">
                        @foreach (var employee in Model.EmployeesOutOfBuilding.Take(10))
                        {
                            <div class="employee-item">
                                <div class="employee-info">
                                    <strong>@employee.Employee.FirstName @employee.Employee.LastName</strong>
                                    <small class="text-muted d-block">@employee.Employee.PersonnelCode</small>
                                    @if (!string.IsNullOrEmpty(employee.ExitPermitNumber))
                                    {
                                        <small class="text-info d-block">برگه: @employee.ExitPermitNumber</small>
                                    }
                                </div>
                                <div class="employee-status">
                                    <span class="badge @employee.GetStatusBadgeClass()">
                                        @employee.GetStatusDisplayName()
                                    </span>
                                    <small class="text-muted d-block">@employee.LastUpdated.ToString("HH:mm")</small>
                                </div>
                            </div>
                        }
                        @if (Model.EmployeesOutOfBuilding.Count > 10)
                        {
                            <div class="text-center mt-3">
                                <a href="/reports/out-of-building" class="btn btn-sm btn-outline-warning">
                                    مشاهده همه (@Model.EmployeesOutOfBuilding.Count نفر)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        <p class="mt-2">همه کارمندان در ساختمان حضور دارند</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- خودروهای داخل و خارج پارکینگ -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-car-front-fill"></i> خودروهای داخل پارکینگ
                </h5>
                <span class="badge bg-success">@Model.CarsInParking.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.CarsInParking.Any())
                {
                    <div class="car-list">
                        @foreach (var car in Model.CarsInParking.Take(8))
                        {
                            <div class="car-item">
                                <div class="car-info">
                                    <strong>@car.PlateNumber</strong>
                                    <small class="text-muted d-block">@car.GetCarType()</small>
                                    @if (car.DriverEmployee != null)
                                    {
                                        <small class="text-info d-block">@car.DriverEmployee.FirstName @car.DriverEmployee.LastName</small>
                                    }
                                    else if (!string.IsNullOrEmpty(car.GuestName))
                                    {
                                        <small class="text-warning d-block">@car.GuestName</small>
                                    }
                                </div>
                                <div class="car-status">
                                    <span class="badge @car.GetStatusBadgeClass()">
                                        <i class="@car.GetStatusIcon()"></i>
                                        @car.GetStatusDisplayName()
                                    </span>
                                    <small class="text-muted d-block">ورود: @car.EntryTime.ToString("HH:mm")</small>
                                </div>
                            </div>
                        }
                        @if (Model.CarsInParking.Count > 8)
                        {
                            <div class="text-center mt-3">
                                <a href="/traffic/car-exit" class="btn btn-sm btn-outline-success">
                                    مشاهده همه (@Model.CarsInParking.Count خودرو)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-car-front" style="font-size: 2rem;"></i>
                        <p class="mt-2">هیچ خودرویی در پارکینگ نیست</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-car-front"></i> خودروهای خارج از پارکینگ
                </h5>
                <span class="badge bg-secondary">@Model.CarsOutOfParking.Count خودرو</span>
            </div>
            <div class="card-body">
                @if (Model.CarsOutOfParking.Any())
                {
                    <div class="car-list">
                        @foreach (var car in Model.CarsOutOfParking.Take(8))
                        {
                            <div class="car-item">
                                <div class="car-info">
                                    <strong>@car.PlateNumber</strong>
                                    <small class="text-muted d-block">@car.GetCarType()</small>
                                    @if (car.DriverEmployee != null)
                                    {
                                        <small class="text-info d-block">@car.DriverEmployee.FirstName @car.DriverEmployee.LastName</small>
                                    }
                                    else if (!string.IsNullOrEmpty(car.GuestName))
                                    {
                                        <small class="text-warning d-block">@car.GuestName</small>
                                    }
                                </div>
                                <div class="car-status">
                                    <span class="badge @car.GetStatusBadgeClass()">
                                        <i class="@car.GetStatusIcon()"></i>
                                        @car.GetStatusDisplayName()
                                    </span>
                                    @if (car.ExitTime.HasValue)
                                    {
                                        <small class="text-muted d-block">خروج: @car.ExitTime.Value.ToString("HH:mm")</small>
                                    }
                                </div>
                            </div>
                        }
                        @if (Model.CarsOutOfParking.Count > 8)
                        {
                            <div class="text-center mt-3">
                                <a href="/reports/car-status" class="btn btn-sm btn-outline-secondary">
                                    مشاهده همه (@Model.CarsOutOfParking.Count خودرو)
                                </a>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        <p class="mt-2">همه خودروها در پارکینگ هستند</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .dashboard-date {
        text-align: center;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stats-icon {
        font-size: 2.5rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 1rem;
        margin-bottom: 0.25rem;
        font-weight: 600;
    }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .stats-absent {
        border-right: 4px solid #dc3545;
    }
    .stats-absent .stats-icon { color: #dc3545; }

    .stats-hourly-exit {
        border-right: 4px solid #ffc107;
    }
    .stats-hourly-exit .stats-icon { color: #ffc107; }

    .stats-late {
        border-right: 4px solid #fd7e14;
    }
    .stats-late .stats-icon { color: #fd7e14; }

    .quick-action-btn {
        display: block;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        text-decoration: none;
        color: #495057;
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
    }

    .quick-action-btn:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .quick-action-btn i {
        font-size: 2rem;
        display: block;
        margin-bottom: 0.5rem;
    }

    .quick-action-btn span {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .employee-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .employee-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.2s ease;
    }

    .employee-item:hover {
        background-color: #f8f9fa;
    }

    .employee-item:last-child {
        border-bottom: none;
    }

    .employee-info strong {
        color: #2c3e50;
    }

    .employee-status {
        text-align: left;
    }

    .car-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .car-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.2s ease;
    }

    .car-item:hover {
        background-color: #f8f9fa;
    }

    .car-item:last-child {
        border-bottom: none;
    }

    .car-info strong {
        color: #2c3e50;
    }

    .car-status {
        text-align: left;
    }

    .card {
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-radius: 15px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 15px 15px 0 0 !important;
        padding: 1rem 1.5rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .auto-refresh-indicator {
        position: fixed;
        top: 20px;
        left: 20px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        z-index: 1000;
        display: none;
    }

    .auto-refresh-indicator.show {
        display: block;
    }
</style>

<div class="auto-refresh-indicator" id="refreshIndicator">
    <i class="bi bi-arrow-clockwise"></i> به‌روزرسانی خودکار...
</div>

<script>
    // Auto-refresh every 30 seconds
    let refreshInterval;
    let lastRefresh = Date.now();

    function startAutoRefresh() {
        refreshInterval = setInterval(() => {
            // نمایش نشانگر به‌روزرسانی
            const indicator = document.getElementById('refreshIndicator');
            indicator.classList.add('show');

            // به‌روزرسانی صفحه
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }, 30000); // هر 30 ثانیه
    }

    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    }

    // شروع auto-refresh
    document.addEventListener('DOMContentLoaded', function() {
        startAutoRefresh();

        // توقف auto-refresh هنگام خروج از صفحه
        window.addEventListener('beforeunload', stopAutoRefresh);

        // توقف auto-refresh هنگام کلیک روی لینک‌ها
        document.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', stopAutoRefresh);
        });

        // نمایش زمان آخرین به‌روزرسانی
        const now = new Date();
        console.log('Dashboard loaded at:', now.toLocaleTimeString('fa-IR'));
    });

    // Manual refresh function
    function manualRefresh() {
        const indicator = document.getElementById('refreshIndicator');
        indicator.innerHTML = '<i class="bi bi-arrow-clockwise"></i> به‌روزرسانی دستی...';
        indicator.classList.add('show');

        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    // Keyboard shortcut for manual refresh (F5 or Ctrl+R)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
            e.preventDefault();
            manualRefresh();
        }
    });
</script>
