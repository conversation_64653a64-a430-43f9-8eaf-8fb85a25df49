using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    public class CreateModel : PageModel
    {
        private readonly BuildingService _buildingService;

        public CreateModel(BuildingService buildingService)
        {
            _buildingService = buildingService;
        }

        [BindProperty]
        public Building Building { get; set; } = default!;

        public IActionResult OnGet()
        {
            Building = new Building();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Check if building name already exists
            bool buildingExists = await _buildingService.BuildingExistsAsync(Building.Name, null);
            if (buildingExists)
            {
                ModelState.AddModelError("Building.Name", "نام ساختمان وارد شده قبلاً ثبت شده است.");
                return Page();
            }

            try
            {
                await _buildingService.AddBuildingAsync(Building);
                TempData["SuccessMessage"] = "ساختمان جدید با موفقیت اضافه شد.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError(string.Empty, "خطا در ذخیره اطلاعات: " + ex.Message);
                return Page();
            }
        }
    }
}
