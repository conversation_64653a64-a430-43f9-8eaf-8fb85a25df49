@page "/traffic/individual-entry"
@model EmployeeTrafficControl.Web.Pages.Traffic.IndividualEntryModel
@{
    ViewData["Title"] = "ورود کارمندان (فردی)";
}

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h1><i class="bi bi-box-arrow-in-right"></i> ورود کارمندان (فردی)</h1>
        <div>
            <a href="/dashboard" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> بازگشت به داشبورد
            </a>
        </div>
    </div>
    <p class="text-muted">ثبت ورود فردی کارمندان در طول روز</p>
</div>

<!-- فیلتر و جستجو -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">جستجو در کارمندان</label>
                        <input type="text" name="SearchTerm" value="@Model.SearchTerm" class="form-control" 
                               placeholder="نام، نام خانوادگی یا کد پرسنلی" />
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">ساختمان</label>
                        <select name="BuildingId" class="form-select" asp-items="Model.Buildings">
                            <option value="">همه ساختمان‌ها</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">وضعیت</label>
                        <select name="StatusFilter" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="out" selected="@(Model.StatusFilter == "out")">خارج از ساختمان</option>
                            <option value="hourly" selected="@(Model.StatusFilter == "hourly")">خروج ساعتی</option>
                            <option value="mission" selected="@(Model.StatusFilter == "mission")">ماموریت اداری</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- آمار سریع -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card stats-out">
            <div class="stats-icon">
                <i class="bi bi-person-x"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.OutOfOfficeCount</h3>
                <p>خارج از ساختمان</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-hourly">
            <div class="stats-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.HourlyExitCount</h3>
                <p>خروج ساعتی</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-mission">
            <div class="stats-icon">
                <i class="bi bi-briefcase"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.MissionCount</h3>
                <p>ماموریت اداری</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card stats-present">
            <div class="stats-icon">
                <i class="bi bi-person-check"></i>
            </div>
            <div class="stats-content">
                <h3>@Model.PresentCount</h3>
                <p>حاضر در ساختمان</p>
            </div>
        </div>
    </div>
</div>

<!-- لیست کارمندان -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> کارمندان قابل ورود
                </h5>
                <span class="badge bg-info">@Model.EmployeesCanEnter.Count نفر</span>
            </div>
            <div class="card-body">
                @if (Model.EmployeesCanEnter.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>کارمند</th>
                                    <th>کد پرسنلی</th>
                                    <th>ساختمان</th>
                                    <th>شغل</th>
                                    <th>وضعیت فعلی</th>
                                    <th>آخرین فعالیت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var status in Model.EmployeesCanEnter)
                                {
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>@status.Employee.FirstName @status.Employee.LastName</strong>
                                                @if (!string.IsNullOrEmpty(status.Employee.PhoneNumber))
                                                {
                                                    <br><small class="text-muted">@status.Employee.PhoneNumber</small>
                                                }
                                            </div>
                                        </td>
                                        <td>
                                            <code>@status.Employee.PersonnelCode</code>
                                        </td>
                                        <td>
                                            @if (status.Employee.Building != null)
                                            {
                                                <span class="badge bg-info">@status.Employee.Building.Name</span>
                                            }
                                        </td>
                                        <td>
                                            @if (status.Employee.Job != null)
                                            {
                                                <span class="badge bg-secondary">@status.Employee.Job.Title</span>
                                                @if (status.Employee.Job.IsDriver)
                                                {
                                                    <span class="badge bg-warning ms-1">راننده</span>
                                                }
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @status.GetStatusBadgeClass()">
                                                @status.GetStatusDisplayName()
                                            </span>
                                            @if (!string.IsNullOrEmpty(status.ExitPermitNumber))
                                            {
                                                <br><small class="text-info">برگه: @status.ExitPermitNumber</small>
                                            }
                                        </td>
                                        <td>
                                            <small class="text-muted">@status.LastUpdated.ToString("HH:mm")</small>
                                            @if (!string.IsNullOrEmpty(status.Notes))
                                            {
                                                <br><small class="text-muted">@status.Notes</small>
                                            }
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="registerEntry(@status.EmployeeId, '@status.Employee.FirstName @status.Employee.LastName')">
                                                <i class="bi bi-box-arrow-in-right"></i> ثبت ورود
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-check-circle" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">همه کارمندان در ساختمان حضور دارند</h5>
                        <p>هیچ کارمندی برای ورود وجود ندارد.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: none;
        height: 100%;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-left: 1rem;
        opacity: 0.8;
    }

    .stats-content h3 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-content p {
        font-size: 0.9rem;
        margin-bottom: 0;
        font-weight: 600;
    }

    .stats-out {
        border-right: 4px solid #dc3545;
    }
    .stats-out .stats-icon { color: #dc3545; }

    .stats-hourly {
        border-right: 4px solid #ffc107;
    }
    .stats-hourly .stats-icon { color: #ffc107; }

    .stats-mission {
        border-right: 4px solid #17a2b8;
    }
    .stats-mission .stats-icon { color: #17a2b8; }

    .stats-present {
        border-right: 4px solid #28a745;
    }
    .stats-present .stats-icon { color: #28a745; }

    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 10px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }
</style>

<script>
    async function registerEntry(employeeId, employeeName) {
        if (!confirm(`آیا مطمئن هستید که می‌خواهید ورود "${employeeName}" را ثبت کنید؟`)) {
            return;
        }

        try {
            const response = await fetch('/api/traffic/register-entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeIds: [employeeId]
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                alert(result.message || 'ورود با موفقیت ثبت شد');
                location.reload();
            } else {
                alert(result.message || 'خطا در ثبت ورود. لطفاً دوباره تلاش کنید.');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('خطا در ارتباط با سرور.');
        }
    }
</script>
