@page "{id:int}"
@model EmployeeTrafficControl.Web.Pages.Buildings.EditModel
@{
    ViewData["Title"] = "ویرایش ساختمان";
}

<div class="page-header">
    <h1>ویرایش ساختمان</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <input type="hidden" asp-for="Building.BuildingId" />
                    
                    <div class="form-section">
                        <div class="mb-3">
                            <label asp-for="Building.Name" class="form-label">نام ساختمان <span class="text-danger">*</span></label>
                            <input asp-for="Building.Name" class="form-control" placeholder="مثال: ساختمان مرکزی" />
                            <span asp-validation-for="Building.Name" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Building.Description" class="form-label">توضیحات</label>
                            <textarea asp-for="Building.Description" class="form-control" rows="4" 
                                      placeholder="توضیحات مربوط به این ساختمان را وارد کنید..."></textarea>
                            <span asp-validation-for="Building.Description" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره تغییرات
                        </button>
                        <a asp-page="Details" asp-route-id="@Model.Building.BuildingId" class="btn btn-info">
                            <i class="bi bi-eye"></i> مشاهده جزئیات
                        </a>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">اطلاعات ساختمان</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-5">شناسه:</dt>
                    <dd class="col-sm-7">@Model.Building.BuildingId</dd>
                    
                    <dt class="col-sm-5">تعداد کارمندان:</dt>
                    <dd class="col-sm-7">
                        <span class="badge bg-info">@Model.Building.Employees.Count نفر</span>
                    </dd>
                </dl>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        نام ساختمان باید منحصر به فرد باشد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
