@page "{buildingId:int?}"
@model EmployeeTrafficControl.Web.Pages.Employees.CreateModel
@{
    ViewData["Title"] = "افزودن کارمند جدید";
}

<div class="page-header">
    <h1>افزودن کارمند جدید</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="form-section">
                        <h5 class="border-bottom pb-2 mb-3">اطلاعات شخصی</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Employee.FirstName" class="form-label">نام <span class="text-danger">*</span></label>
                                    <input asp-for="Employee.FirstName" class="form-control" placeholder="نام کارمند" />
                                    <span asp-validation-for="Employee.FirstName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Employee.LastName" class="form-label">نام خانوادگی <span class="text-danger">*</span></label>
                                    <input asp-for="Employee.LastName" class="form-control" placeholder="نام خانوادگی کارمند" />
                                    <span asp-validation-for="Employee.LastName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Employee.NationalCode" class="form-label">کد ملی</label>
                                    <input asp-for="Employee.NationalCode" class="form-control" placeholder="1234567890" maxlength="10" />
                                    <span asp-validation-for="Employee.NationalCode" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Employee.PersonnelCode" class="form-label">کد پرسنلی <span class="text-danger">*</span></label>
                                    <input asp-for="Employee.PersonnelCode" class="form-control" placeholder="EMP001" />
                                    <span asp-validation-for="Employee.PersonnelCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Employee.PhoneNumber" class="form-label">شماره تماس</label>
                            <input asp-for="Employee.PhoneNumber" class="form-control" placeholder="09121234567" />
                            <span asp-validation-for="Employee.PhoneNumber" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5 class="border-bottom pb-2 mb-3">اطلاعات شغلی</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Employee.BuildingId" class="form-label">ساختمان <span class="text-danger">*</span></label>
                                    <select asp-for="Employee.BuildingId" class="form-select" asp-items="Model.Buildings">
                                        <option value="">انتخاب کنید...</option>
                                    </select>
                                    <span asp-validation-for="Employee.BuildingId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Employee.JobId" class="form-label">شغل <span class="text-danger">*</span></label>
                                    <select asp-for="Employee.JobId" class="form-select" asp-items="Model.Jobs">
                                        <option value="">انتخاب کنید...</option>
                                    </select>
                                    <span asp-validation-for="Employee.JobId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="Employee.IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="Employee.IsActive" class="form-check-label">
                                    کارمند فعال است
                                </label>
                            </div>
                            <span asp-validation-for="Employee.IsActive" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> ذخیره
                        </button>
                        <a asp-page="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> بازگشت به لیست
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">راهنما</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        کد ملی و کد پرسنلی باید منحصر به فرد باشند
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        فیلدهای دارای علامت * اجباری هستند
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-info-circle text-info"></i>
                        کد ملی باید دقیقاً 10 رقم باشد
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
