using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    public class IndexModel : PageModel
    {
        private readonly BuildingService _buildingService;

        public IndexModel(BuildingService buildingService)
        {
            _buildingService = buildingService;
        }

        public IList<Building> Buildings { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Buildings = await _buildingService.GetAllBuildingsAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var building = await _buildingService.GetBuildingByIdAsync(id);
            if (building != null)
            {
                await _buildingService.DeleteBuildingAsync(id);
                TempData["SuccessMessage"] = "ساختمان با موفقیت حذف شد.";
            }
            else
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
            }

            return RedirectToPage();
        }
    }
}
