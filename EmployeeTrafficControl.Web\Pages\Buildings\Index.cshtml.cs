using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Buildings
{
    public class IndexModel : PageModel
    {
        private readonly BuildingService _buildingService;

        public IndexModel(BuildingService buildingService)
        {
            _buildingService = buildingService;
        }

        public IList<Building> Buildings { get; set; } = default!;

        public async Task OnGetAsync()
        {
            Buildings = await _buildingService.GetAllBuildingsAsync();
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var building = await _buildingService.GetBuildingByIdAsync(id);
            if (building == null)
            {
                TempData["ErrorMessage"] = "ساختمان مورد نظر یافت نشد.";
                return RedirectToPage();
            }

            // Check if there are employees in this building
            if (building.Employees != null && building.Employees.Any())
            {
                TempData["ErrorMessage"] = $"امکان حذف این ساختمان وجود ندارد زیرا {building.Employees.Count} کارمند در این ساختمان کار می‌کنند.";
                return RedirectToPage();
            }

            try
            {
                bool deleteResult = await _buildingService.DeleteBuildingAsync(id);
                if (deleteResult)
                {
                    TempData["SuccessMessage"] = "ساختمان با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این ساختمان وجود ندارد.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف ساختمان: " + ex.Message;
            }

            return RedirectToPage();
        }
    }
}
