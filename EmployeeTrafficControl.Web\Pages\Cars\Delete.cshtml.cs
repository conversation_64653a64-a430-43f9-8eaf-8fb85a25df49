using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    public class DeleteModel : PageModel
    {
        private readonly CarService _carService;

        public DeleteModel(CarService carService)
        {
            _carService = carService;
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var car = await _carService.GetCarByIdAsync(id);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _carService.DeleteCarAsync(id);
                TempData["SuccessMessage"] = "خودرو با موفقیت حذف شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف خودرو: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
