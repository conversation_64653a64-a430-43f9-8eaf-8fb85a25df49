using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Cars
{
    public class DeleteModel : PageModel
    {
        private readonly CarService _carService;

        public DeleteModel(CarService carService)
        {
            _carService = carService;
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var car = await _carService.GetCarByIdAsync(id);
            if (car == null)
            {
                TempData["ErrorMessage"] = "خودرو مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                bool deleteResult = await _carService.DeleteCarAsync(id);
                if (deleteResult)
                {
                    TempData["SuccessMessage"] = "خودرو با موفقیت حذف شد.";
                }
                else
                {
                    TempData["ErrorMessage"] = "امکان حذف این خودرو وجود ندارد زیرا دارای سوابق تردد است.";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف خودرو: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
