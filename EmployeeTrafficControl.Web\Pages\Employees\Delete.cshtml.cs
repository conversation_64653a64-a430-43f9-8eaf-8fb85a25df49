using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Employees
{
    public class DeleteModel : PageModel
    {
        private readonly EmployeeService _employeeService;

        public DeleteModel(EmployeeService employeeService)
        {
            _employeeService = employeeService;
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var employee = await _employeeService.GetEmployeeByIdAsync(id);
            if (employee == null)
            {
                TempData["ErrorMessage"] = "کارمند مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _employeeService.DeleteEmployeeAsync(id);
                TempData["SuccessMessage"] = "کارمند با موفقیت حذف شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف کارمند: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
