﻿// <auto-generated />
using System;
using EmployeeTrafficControl.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EmployeeTrafficControl.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250608220627_AddBuildingIdToCarWithData")]
    partial class AddBuildingIdToCarWithData
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EmployeeTrafficControl.Models.Building", b =>
                {
                    b.Property<int>("BuildingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BuildingId"));

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("BuildingId");

                    b.ToTable("Buildings");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Car", b =>
                {
                    b.Property<int>("CarId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Model")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("PassengerCapacity")
                        .HasColumnType("int");

                    b.Property<string>("PlateNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("CarId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("PlateNumber")
                        .IsUnique();

                    b.ToTable("Cars");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.CarPassenger", b =>
                {
                    b.Property<int>("CarPassengerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarPassengerId"));

                    b.Property<int>("CarTrafficLogId")
                        .HasColumnType("int");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<bool>("IsEntered")
                        .HasColumnType("bit");

                    b.HasKey("CarPassengerId");

                    b.HasIndex("CarTrafficLogId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("CarPassengers");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.CarTrafficLog", b =>
                {
                    b.Property<int>("CarTrafficLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarTrafficLogId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<int?>("CarId")
                        .HasColumnType("int");

                    b.Property<string>("CurrentStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("DriverEmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExitTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("GuestName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("PlateNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("CarTrafficLogId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("CarId");

                    b.HasIndex("DriverEmployeeId");

                    b.ToTable("CarTrafficLogs");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.DailyAttendance", b =>
                {
                    b.Property<int>("AttendanceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AttendanceId"));

                    b.Property<DateTime?>("ApprovalTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ApprovedByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CheckInTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CheckOutTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("EarlyLeaveMinutes")
                        .HasColumnType("int");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<int>("HourlyExitCount")
                        .HasColumnType("int");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPresent")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int>("LateMinutes")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("RegisteredByUserId")
                        .HasColumnType("int");

                    b.Property<DateTime>("RegisteredTime")
                        .HasColumnType("datetime2");

                    b.Property<TimeSpan>("TotalHourlyExitTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan?>("TotalWorkHours")
                        .HasColumnType("time");

                    b.HasKey("AttendanceId");

                    b.HasIndex("ApprovedByUserId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("RegisteredByUserId");

                    b.ToTable("DailyAttendances");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Employee", b =>
                {
                    b.Property<int>("EmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EmployeeId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("HasDrivingLicense")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("JobId")
                        .HasColumnType("int");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NationalCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("PersonnelCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("EmployeeId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("JobId");

                    b.HasIndex("NationalCode")
                        .IsUnique()
                        .HasFilter("[NationalCode] IS NOT NULL");

                    b.HasIndex("PersonnelCode")
                        .IsUnique();

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.EmployeeStatus", b =>
                {
                    b.Property<int>("StatusId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StatusId"));

                    b.Property<int>("CurrentStatus")
                        .HasColumnType("int");

                    b.Property<int?>("CurrentVehicleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExitPermitNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("FinalExitTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsInVehicle")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("UpdatedByUserId")
                        .HasColumnType("int");

                    b.HasKey("StatusId");

                    b.HasIndex("CurrentVehicleId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("EmployeeStatuses");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.EmployeeWorkingHours", b =>
                {
                    b.Property<int>("EmployeeWorkingHoursId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EmployeeWorkingHoursId"));

                    b.Property<TimeSpan>("CustomEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("CustomStartTime")
                        .HasColumnType("time");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("date");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("date");

                    b.HasKey("EmployeeWorkingHoursId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("EmployeeWorkingHours");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.GuestCarTrafficLog", b =>
                {
                    b.Property<int>("GuestCarTrafficLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("GuestCarTrafficLogId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExitTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PlateNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("GuestCarTrafficLogId");

                    b.HasIndex("BuildingId");

                    b.ToTable("GuestCarTrafficLogs");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Job", b =>
                {
                    b.Property<int>("JobId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("JobId"));

                    b.Property<bool>("IsDriver")
                        .HasColumnType("bit");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("JobId");

                    b.ToTable("Jobs");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.SystemSettings", b =>
                {
                    b.Property<int>("SettingsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettingsId"));

                    b.Property<bool>("AutoRegisterEntry")
                        .HasColumnType("bit");

                    b.Property<bool>("AutoRegisterExit")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LastUpdatedByUserId")
                        .HasColumnType("int");

                    b.Property<int>("MaxEarlyLeaveMinutes")
                        .HasColumnType("int");

                    b.Property<int>("MaxHourlyExitHours")
                        .HasColumnType("int");

                    b.Property<int>("MaxLateMinutes")
                        .HasColumnType("int");

                    b.Property<string>("OrganizationAddress")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("OrganizationEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OrganizationName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("OrganizationPhone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("RequireApprovalForHourlyExit")
                        .HasColumnType("bit");

                    b.Property<TimeSpan>("WorkEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("WorkStartTime")
                        .HasColumnType("time");

                    b.Property<string>("WorkingDays")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("SettingsId");

                    b.HasIndex("LastUpdatedByUserId");

                    b.ToTable("SystemSettings");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.TrafficLog", b =>
                {
                    b.Property<int>("TrafficLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TrafficLogId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EntryTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExitTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsAbsence")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAutomaticExit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEarlyExit")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLate")
                        .HasColumnType("bit");

                    b.Property<string>("TrafficType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("TrafficLogId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("EmployeeId");

                    b.ToTable("TrafficLogs");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserId"));

                    b.Property<int?>("BuildingId")
                        .HasColumnType("int");

                    b.Property<int?>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("UserId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("EmployeeId")
                        .IsUnique()
                        .HasFilter("[EmployeeId] IS NOT NULL");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.UserSession", b =>
                {
                    b.Property<int>("SessionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SessionId"));

                    b.Property<DateTime>("ExpiryTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExpired")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastActivity")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LoginTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LogoutTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("SessionToken")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("SessionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSessions");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.WorkingHoursSetting", b =>
                {
                    b.Property<int>("SettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettingId"));

                    b.Property<int>("BuildingId")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("DefaultEndTime")
                        .HasColumnType("time");

                    b.Property<TimeSpan>("DefaultStartTime")
                        .HasColumnType("time");

                    b.Property<int>("EarlyExitToleranceMinutes")
                        .HasColumnType("int");

                    b.Property<int>("LateEntryToleranceMinutes")
                        .HasColumnType("int");

                    b.HasKey("SettingId");

                    b.HasIndex("BuildingId")
                        .IsUnique();

                    b.ToTable("WorkingHoursSettings");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Car", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithMany()
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.CarPassenger", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.CarTrafficLog", "CarTrafficLog")
                        .WithMany("CarPassengers")
                        .HasForeignKey("CarTrafficLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Models.Employee", "Employee")
                        .WithMany("CarPassengers")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CarTrafficLog");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.CarTrafficLog", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithMany("CarTrafficLogs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Models.Car", "Car")
                        .WithMany("CarTrafficLogs")
                        .HasForeignKey("CarId");

                    b.HasOne("EmployeeTrafficControl.Models.Employee", "DriverEmployee")
                        .WithMany("CarTrafficLogsAsDriver")
                        .HasForeignKey("DriverEmployeeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Building");

                    b.Navigation("Car");

                    b.Navigation("DriverEmployee");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.DailyAttendance", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.User", "ApprovedByUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByUserId");

                    b.HasOne("EmployeeTrafficControl.Models.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Models.User", "RegisteredByUser")
                        .WithMany()
                        .HasForeignKey("RegisteredByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedByUser");

                    b.Navigation("Employee");

                    b.Navigation("RegisteredByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Employee", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithMany("Employees")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Models.Job", "Job")
                        .WithMany("Employees")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("Job");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.EmployeeStatus", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Car", "CurrentVehicle")
                        .WithMany()
                        .HasForeignKey("CurrentVehicleId");

                    b.HasOne("EmployeeTrafficControl.Models.Employee", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Models.User", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId");

                    b.Navigation("CurrentVehicle");

                    b.Navigation("Employee");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.EmployeeWorkingHours", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Employee", "Employee")
                        .WithMany("EmployeeWorkingHours")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.GuestCarTrafficLog", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithMany("GuestCarTrafficLogs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.SystemSettings", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.User", "LastUpdatedByUser")
                        .WithMany()
                        .HasForeignKey("LastUpdatedByUserId");

                    b.Navigation("LastUpdatedByUser");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.TrafficLog", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithMany("TrafficLogs")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EmployeeTrafficControl.Models.Employee", "Employee")
                        .WithMany("TrafficLogs")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.User", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithMany("Users")
                        .HasForeignKey("BuildingId");

                    b.HasOne("EmployeeTrafficControl.Models.Employee", "Employee")
                        .WithOne("User")
                        .HasForeignKey("EmployeeTrafficControl.Models.User", "EmployeeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Building");

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.UserSession", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.WorkingHoursSetting", b =>
                {
                    b.HasOne("EmployeeTrafficControl.Models.Building", "Building")
                        .WithOne("WorkingHoursSetting")
                        .HasForeignKey("EmployeeTrafficControl.Models.WorkingHoursSetting", "BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Building");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Building", b =>
                {
                    b.Navigation("CarTrafficLogs");

                    b.Navigation("Employees");

                    b.Navigation("GuestCarTrafficLogs");

                    b.Navigation("TrafficLogs");

                    b.Navigation("Users");

                    b.Navigation("WorkingHoursSetting");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Car", b =>
                {
                    b.Navigation("CarTrafficLogs");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.CarTrafficLog", b =>
                {
                    b.Navigation("CarPassengers");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Employee", b =>
                {
                    b.Navigation("CarPassengers");

                    b.Navigation("CarTrafficLogsAsDriver");

                    b.Navigation("EmployeeWorkingHours");

                    b.Navigation("TrafficLogs");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EmployeeTrafficControl.Models.Job", b =>
                {
                    b.Navigation("Employees");
                });
#pragma warning restore 612, 618
        }
    }
}
