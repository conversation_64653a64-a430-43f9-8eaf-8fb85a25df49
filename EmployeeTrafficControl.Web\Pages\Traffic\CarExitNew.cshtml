@page "/traffic/car-exit-new"
@model EmployeeTrafficControl.Web.Pages.Traffic.CarExitNewModel
@{
    ViewData["Title"] = "خروج خودرو";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="dashboard-title">
                    <i class="bi bi-car-front-fill"></i> خروج خودرو
                </h1>
                <p class="dashboard-subtitle">ثبت خروج خودرو از پارکینگ ساختمان</p>
            </div>
            <div class="col-md-4">
                <div class="dashboard-date">
                    <i class="bi bi-calendar3"></i>
                    @DateTime.Now.ToString("yyyy/MM/dd", new System.Globalization.CultureInfo("fa-IR"))
                    <br>
                    <small>@DateTime.Now.ToString("HH:mm")</small>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.Message))
    {
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            @Model.Message
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- فرم انتخاب خودرو -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-1-circle"></i> انتخاب خودرو
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" asp-page-handler="SelectCar">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">خودرو</label>
                                <select name="CarId" class="form-select" required>
                                    <option value="">انتخاب خودرو...</option>
                                    @foreach (var car in Model.AvailableCars)
                                    {
                                        <option value="@car.CarId" selected="@(car.CarId == Model.SelectedCarId)">
                                            @car.PlateNumber - @car.Model
                                            @if (car.IsMoneyTransport)
                                            {
                                                <text>(پولرسان)</text>
                                            }
                                        </option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-arrow-right"></i> ادامه
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @if (Model.SelectedCar != null)
    {
        <!-- فرم جزئیات خروج -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-2-circle"></i> جزئیات خروج - @Model.SelectedCar.PlateNumber
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" asp-page-handler="RegisterExit" id="exitForm">
                            <input type="hidden" name="CarId" value="@Model.SelectedCarId" />
                            
                            <div class="row g-3 mb-4">
                                <!-- انتخاب راننده -->
                                <div class="col-md-6">
                                    <label class="form-label">راننده <span class="text-danger">*</span></label>
                                    <select name="DriverId" class="form-select" required>
                                        <option value="">انتخاب راننده...</option>
                                        @foreach (var driver in Model.AvailableDrivers)
                                        {
                                            <option value="@driver.EmployeeId">
                                                @driver.Employee.FirstName @driver.Employee.LastName - @driver.Employee.PersonnelCode
                                            </option>
                                        }
                                    </select>
                                </div>

                                <!-- نوع خروج -->
                                <div class="col-md-6">
                                    <label class="form-label">نوع خروج <span class="text-danger">*</span></label>
                                    <select name="ExitType" class="form-select" required id="exitTypeSelect">
                                        <option value="">انتخاب نوع خروج...</option>
                                        <option value="اداری">اداری</option>
                                        <option value="ماموریت">ماموریت</option>
                                        @if (Model.SelectedCar.IsMoneyTransport)
                                        {
                                            <option value="پولرسانی">پولرسانی</option>
                                        }
                                    </select>
                                </div>
                            </div>

                            <!-- کیلومتر (فقط برای پولرسان در اولین خروج روز) -->
                            @if (Model.SelectedCar.IsMoneyTransport && Model.IsFirstExitToday)
                            {
                                <div class="row g-3 mb-4" id="kilometerSection">
                                    <div class="col-md-6">
                                        <label class="form-label">کیلومتر فعلی خودرو <span class="text-danger">*</span></label>
                                        <input type="number" name="CurrentKilometer" class="form-control" 
                                               placeholder="کیلومتر فعلی" min="0" max="9999999" required />
                                        <small class="text-info">این اولین خروج خودرو پولرسان در روز جاری است</small>
                                    </div>
                                </div>
                            }

                            <!-- انتخاب سرنشینان -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label class="form-label">سرنشینان (حداکثر @(Model.SelectedCar.PassengerCapacity - 1) نفر)</label>
                                    <div id="passengersSection">
                                        <!-- سرنشینان بر اساس نوع خروج نمایش داده می‌شوند -->
                                    </div>
                                </div>
                            </div>

                            <!-- توضیحات -->
                            <div class="row g-3 mb-4">
                                <div class="col-12">
                                    <label class="form-label">توضیحات</label>
                                    <textarea name="Notes" class="form-control" rows="3" 
                                              placeholder="توضیحات اضافی (اختیاری)"></textarea>
                                </div>
                            </div>

                            <!-- دکمه‌های عملیات -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-check-circle"></i> ثبت خروج
                                        </button>
                                        <a href="/dashboard" class="btn btn-secondary">
                                            <i class="bi bi-arrow-left"></i> انصراف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .dashboard-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .dashboard-date {
        text-align: center;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .card {
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-radius: 15px;
    }

    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 15px 15px 0 0 !important;
        padding: 1rem 1.5rem;
    }

    .passenger-checkbox {
        margin-bottom: 0.5rem;
    }

    .passenger-checkbox label {
        font-weight: normal;
        margin-bottom: 0;
        cursor: pointer;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const exitTypeSelect = document.getElementById('exitTypeSelect');
        const passengersSection = document.getElementById('passengersSection');
        
        if (exitTypeSelect && passengersSection) {
            exitTypeSelect.addEventListener('change', function() {
                loadPassengers(this.value);
            });
        }
    });

    async function loadPassengers(exitType) {
        const passengersSection = document.getElementById('passengersSection');
        
        if (!exitType) {
            passengersSection.innerHTML = '<p class="text-muted">ابتدا نوع خروج را انتخاب کنید</p>';
            return;
        }

        try {
            const response = await fetch(`/api/traffic/get-available-passengers?exitType=${exitType}&buildingId=@Model.SelectedCar?.BuildingId`);
            const passengers = await response.json();
            
            if (passengers.length === 0) {
                passengersSection.innerHTML = '<p class="text-muted">هیچ کارمند مناسبی برای این نوع خروج یافت نشد</p>';
                return;
            }

            let html = '<div class="row">';
            passengers.forEach(passenger => {
                html += `
                    <div class="col-md-6 passenger-checkbox">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="PassengerIds" value="${passenger.employeeId}" id="passenger_${passenger.employeeId}">
                            <label class="form-check-label" for="passenger_${passenger.employeeId}">
                                ${passenger.firstName} ${passenger.lastName} - ${passenger.personnelCode}
                                <small class="text-muted d-block">${passenger.jobTitle}</small>
                            </label>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            passengersSection.innerHTML = html;
            
        } catch (error) {
            console.error('Error loading passengers:', error);
            passengersSection.innerHTML = '<p class="text-danger">خطا در بارگذاری لیست کارمندان</p>';
        }
    }
</script>
