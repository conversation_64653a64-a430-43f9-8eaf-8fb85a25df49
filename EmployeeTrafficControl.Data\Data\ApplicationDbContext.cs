﻿using EmployeeTrafficControl.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeTrafficControl.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Building> Buildings { get; set; }
        public DbSet<Job> Jobs { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }
        public DbSet<EmployeeStatus> EmployeeStatuses { get; set; }
        public DbSet<DailyAttendance> DailyAttendances { get; set; }
        public DbSet<WorkingHoursSetting> WorkingHoursSettings { get; set; }
        public DbSet<EmployeeWorkingHours> EmployeeWorkingHours { get; set; }
        public DbSet<TrafficLog> TrafficLogs { get; set; }
        public DbSet<Car> Cars { get; set; }
        public DbSet<CarTrafficLog> CarTrafficLogs { get; set; }
        public DbSet<CarPassenger> CarPassengers { get; set; }
        public DbSet<GuestCarTrafficLog> GuestCarTrafficLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure unique constraints
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.NationalCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.PersonnelCode)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasOne(u => u.Employee)
                .WithOne(e => e.User)
                .HasForeignKey<User>(u => u.EmployeeId)
                .IsRequired(false) // EmployeeId can be null for admin users etc.
                .OnDelete(DeleteBehavior.Restrict); // Prevent cascading delete of Employee if User is deleted

            modelBuilder.Entity<WorkingHoursSetting>()
                .HasIndex(whs => whs.BuildingId)
                .IsUnique();

            modelBuilder.Entity<Car>()
                .HasIndex(c => c.PlateNumber)
                .IsUnique();

            // Configure relationships
            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Building)
                .WithMany(b => b.Employees)
                .HasForeignKey(e => e.BuildingId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent deleting a building if employees are assigned

            modelBuilder.Entity<Employee>()
                .HasOne(e => e.Job)
                .WithMany(j => j.Employees)
                .HasForeignKey(e => e.JobId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent deleting a job if employees are assigned

            modelBuilder.Entity<Car>()
                .HasOne(c => c.Building)
                .WithMany(b => b.Cars)
                .HasForeignKey(c => c.BuildingId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent deleting a building if cars are assigned

            modelBuilder.Entity<CarTrafficLog>()
                .HasOne(ctl => ctl.DriverEmployee)
                .WithMany(e => e.CarTrafficLogsAsDriver)
                .HasForeignKey(ctl => ctl.DriverEmployeeId)
                .OnDelete(DeleteBehavior.Restrict); // Prevent deleting employee if they were a driver

            // For other relationships, EF Core's default conventions are usually fine (e.g., cascade delete for many-to-one children by default)
            // But for critical relationships, explicit configuration with DeleteBehavior.Restrict is safer
            // Example:
            // modelBuilder.Entity<TrafficLog>()
            //     .HasOne(tl => tl.Employee)
            //     .WithMany(e => e.TrafficLogs)
            //     .HasForeignKey(tl => tl.EmployeeId)
            //     .OnDelete(DeleteBehavior.Cascade); // If employee is deleted, their traffic logs are deleted
        }
    }
}