using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;

namespace EmployeeTrafficControl.Services
{
    public class EmployeeStatusService
    {
        private readonly ApplicationDbContext _context;

        public EmployeeStatusService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// دریافت وضعیت فعلی کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>وضعیت کارمند</returns>
        public async Task<EmployeeStatus?> GetEmployeeStatusAsync(int employeeId, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            return await _context.EmployeeStatuses
                                 .Include(es => es.Employee)
                                 .Include(es => es.CurrentVehicle)
                                 .Include(es => es.UpdatedByUser)
                                 .FirstOrDefaultAsync(es => es.EmployeeId == employeeId && es.Date.Date == targetDate.Date);
        }

        /// <summary>
        /// دریافت لیست کارمندان حاضر در ساختمان
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست کارمندان حاضر</returns>
        public async Task<List<EmployeeStatus>> GetPresentEmployeesAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(es => es.CurrentVehicle)
                               .Where(es => es.Date.Date == targetDate.Date && 
                                          es.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding);

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست کارمندان خارج از ساختمان (خروج ساعتی یا ماموریت)
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست کارمندان خارج از ساختمان</returns>
        public async Task<List<EmployeeStatus>> GetEmployeesOutOfBuildingAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Include(es => es.CurrentVehicle)
                               .Where(es => es.Date.Date == targetDate.Date && 
                                          (es.CurrentStatus == EmployeeCurrentStatus.HourlyExit ||
                                           es.CurrentStatus == EmployeeCurrentStatus.OfficialMission));

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// دریافت لیست رانندگان موجود
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>لیست رانندگان موجود</returns>
        public async Task<List<EmployeeStatus>> GetAvailableDriversAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Job)
                               .Include(es => es.Employee)
                               .ThenInclude(e => e.Building)
                               .Where(es => es.Date.Date == targetDate.Date && 
                                          es.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding &&
                                          (es.Employee.Job.IsDriver || es.Employee.HasDrivingLicense));

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            return await query.OrderBy(es => es.Employee.FirstName)
                             .ThenBy(es => es.Employee.LastName)
                             .ToListAsync();
        }

        /// <summary>
        /// به‌روزرسانی وضعیت کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="newStatus">وضعیت جدید</param>
        /// <param name="userId">شناسه کاربر انجام‌دهنده</param>
        /// <param name="notes">توضیحات</param>
        /// <param name="exitPermitNumber">شماره برگه خروج</param>
        /// <param name="vehicleId">شناسه خودرو (در صورت استفاده)</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> UpdateEmployeeStatusAsync(int employeeId, EmployeeCurrentStatus newStatus, 
            int userId, string? notes = null, string? exitPermitNumber = null, int? vehicleId = null)
        {
            try
            {
                var today = DateTime.Today;
                var existingStatus = await GetEmployeeStatusAsync(employeeId, today);

                if (existingStatus == null)
                {
                    // ایجاد وضعیت جدید
                    existingStatus = new EmployeeStatus
                    {
                        EmployeeId = employeeId,
                        Date = today,
                        CurrentStatus = newStatus,
                        UpdatedByUserId = userId,
                        Notes = notes,
                        ExitPermitNumber = exitPermitNumber,
                        CurrentVehicleId = vehicleId,
                        IsInVehicle = vehicleId.HasValue
                    };

                    // تنظیم زمان ورود اگر وضعیت حضور در ساختمان باشد
                    if (newStatus == EmployeeCurrentStatus.PresentInBuilding && !existingStatus.EntryTime.HasValue)
                    {
                        existingStatus.EntryTime = DateTime.Now;
                    }

                    _context.EmployeeStatuses.Add(existingStatus);
                }
                else
                {
                    // به‌روزرسانی وضعیت موجود
                    existingStatus.CurrentStatus = newStatus;
                    existingStatus.UpdatedByUserId = userId;
                    existingStatus.LastUpdated = DateTime.Now;
                    existingStatus.Notes = notes;
                    existingStatus.ExitPermitNumber = exitPermitNumber;
                    existingStatus.CurrentVehicleId = vehicleId;
                    existingStatus.IsInVehicle = vehicleId.HasValue;

                    // تنظیم زمان ورود اگر برای اولین بار حضور در ساختمان ثبت می‌شود
                    if (newStatus == EmployeeCurrentStatus.PresentInBuilding && !existingStatus.EntryTime.HasValue)
                    {
                        existingStatus.EntryTime = DateTime.Now;
                    }

                    // تنظیم زمان خروج نهایی اگر کارمند خارج از اداره شود
                    if (newStatus == EmployeeCurrentStatus.OutOfOffice)
                    {
                        existingStatus.FinalExitTime = DateTime.Now;
                    }
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating employee status: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// ثبت ورود اولیه کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterEmployeeEntryAsync(int employeeId, int userId)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.PresentInBuilding, userId, "ورود اولیه روزانه");
        }

        /// <summary>
        /// ثبت خروج نهایی کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterEmployeeExitAsync(int employeeId, int userId)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.OutOfOffice, userId, "خروج نهایی");
        }

        /// <summary>
        /// ثبت خروج ساعتی کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="exitPermitNumber">شماره برگه خروج</param>
        /// <param name="notes">توضیحات</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterHourlyExitAsync(int employeeId, int userId, string? exitPermitNumber = null, string? notes = null)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.HourlyExit, userId, notes, exitPermitNumber);
        }

        /// <summary>
        /// ثبت ماموریت اداری کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="userId">شناسه کاربر ثبت‌کننده</param>
        /// <param name="notes">توضیحات ماموریت</param>
        /// <returns>موفقیت عملیات</returns>
        public async Task<bool> RegisterOfficialMissionAsync(int employeeId, int userId, string? notes = null)
        {
            return await UpdateEmployeeStatusAsync(employeeId, EmployeeCurrentStatus.OfficialMission, userId, notes);
        }

        /// <summary>
        /// دریافت آمار وضعیت کارمندان
        /// </summary>
        /// <param name="buildingId">شناسه ساختمان (اختیاری)</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>آمار وضعیت کارمندان</returns>
        public async Task<Dictionary<EmployeeCurrentStatus, int>> GetEmployeeStatusStatsAsync(int? buildingId = null, DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Today;
            
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == targetDate.Date);

            if (buildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == buildingId.Value);
            }

            var stats = await query.GroupBy(es => es.CurrentStatus)
                                  .Select(g => new { Status = g.Key, Count = g.Count() })
                                  .ToListAsync();

            var result = new Dictionary<EmployeeCurrentStatus, int>();
            foreach (EmployeeCurrentStatus status in Enum.GetValues<EmployeeCurrentStatus>())
            {
                result[status] = stats.FirstOrDefault(s => s.Status == status)?.Count ?? 0;
            }

            return result;
        }

        /// <summary>
        /// بررسی امکان خروج کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>true اگر امکان خروج وجود داشته باشد</returns>
        public async Task<bool> CanEmployeeExitAsync(int employeeId, DateTime? date = null)
        {
            var status = await GetEmployeeStatusAsync(employeeId, date);
            return status?.CanExitBuilding() ?? false;
        }

        /// <summary>
        /// بررسی امکان ورود کارمند
        /// </summary>
        /// <param name="employeeId">شناسه کارمند</param>
        /// <param name="date">تاریخ (پیش‌فرض امروز)</param>
        /// <returns>true اگر امکان ورود وجود داشته باشد</returns>
        public async Task<bool> CanEmployeeEnterAsync(int employeeId, DateTime? date = null)
        {
            var status = await GetEmployeeStatusAsync(employeeId, date);
            return status?.CanEnterBuilding() ?? true; // اگر وضعیتی ثبت نشده، می‌تواند وارد شود
        }
    }
}
