using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;

namespace EmployeeTrafficControl.Services
{
    public class DatabaseInitializer
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DatabaseInitializer> _logger;

        public DatabaseInitializer(ApplicationDbContext context, ILogger<DatabaseInitializer> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// راه‌اندازی اولیه دیتابیس
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("شروع راه‌اندازی دیتابیس...");

                // 1. ایجاد دیتابیس اگر وجود نداشته باشد
                await EnsureDatabaseCreatedAsync();

                // 2. اضافه کردن داده‌های اولیه
                await SeedInitialDataAsync();

                _logger.LogInformation("راه‌اندازی دیتابیس با موفقیت تکمیل شد.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در راه‌اندازی دیتابیس: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// اطمینان از وجود دیتابیس
        /// </summary>
        private async Task EnsureDatabaseCreatedAsync()
        {
            var created = await _context.Database.EnsureCreatedAsync();
            if (created)
            {
                _logger.LogInformation("دیتابیس جدید ایجاد شد.");
            }
            else
            {
                // اگر دیتابیس وجود دارد، Migration ها را اعمال کن
                var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
                if (pendingMigrations.Any())
                {
                    _logger.LogInformation("اعمال Migration های معلق...");
                    await _context.Database.MigrateAsync();
                    _logger.LogInformation("Migration ها با موفقیت اعمال شدند.");
                }
            }
        }

        /// <summary>
        /// اضافه کردن داده‌های اولیه
        /// </summary>
        private async Task SeedInitialDataAsync()
        {
            // بررسی اینکه آیا داده‌های اولیه وجود دارند
            if (await _context.Users.AnyAsync())
            {
                _logger.LogInformation("داده‌های اولیه قبلاً وجود دارند.");
                return;
            }

            _logger.LogInformation("اضافه کردن داده‌های اولیه...");

            // 1. ایجاد ساختمان پیش‌فرض
            var defaultBuilding = await CreateDefaultBuildingAsync();

            // 2. ایجاد شغل‌های پیش‌فرض
            var jobs = await CreateDefaultJobsAsync();

            // 3. ایجاد تنظیمات سیستم
            await CreateSystemSettingsAsync();

            // 4. ایجاد سوپرادمین
            await CreateSuperAdminAsync(defaultBuilding, jobs.First());

            // 5. ایجاد کارمندان نمونه
            await CreateSampleEmployeesAsync(defaultBuilding, jobs);

            // 6. ایجاد خودروهای نمونه
            await CreateSampleCarsAsync(defaultBuilding);

            await _context.SaveChangesAsync();
            _logger.LogInformation("داده‌های اولیه با موفقیت اضافه شدند.");
        }

        /// <summary>
        /// ایجاد ساختمان پیش‌فرض
        /// </summary>
        private async Task<Building> CreateDefaultBuildingAsync()
        {
            var building = new Building
            {
                Name = "ساختمان مرکزی",
                Address = "تهران، خیابان آزادی",
                PhoneNumber = "021-12345678"
            };

            _context.Buildings.Add(building);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("ساختمان پیش‌فرض ایجاد شد: {BuildingName}", building.Name);
            return building;
        }

        /// <summary>
        /// ایجاد شغل‌های پیش‌فرض
        /// </summary>
        private async Task<List<Job>> CreateDefaultJobsAsync()
        {
            var jobs = new List<Job>
            {
                new Job { Title = "مدیر سیستم", IsDriver = false },
                new Job { Title = "مدیر ساختمان", IsDriver = false },
                new Job { Title = "نگهبان", IsDriver = true },
                new Job { Title = "خزانه‌دار", IsDriver = false },
                new Job { Title = "راننده", IsDriver = true },
                new Job { Title = "کارمند اداری", IsDriver = false },
                new Job { Title = "تکنسین", IsDriver = false }
            };

            _context.Jobs.AddRange(jobs);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("شغل‌های پیش‌فرض ایجاد شدند: {JobCount} شغل", jobs.Count);
            return jobs;
        }

        /// <summary>
        /// ایجاد تنظیمات سیستم
        /// </summary>
        private async Task CreateSystemSettingsAsync()
        {
            var settings = new SystemSettings
            {
                OrganizationName = "سازمان نمونه",
                MaxLateMinutes = 15,
                MaxEarlyLeaveMinutes = 15,
                WorkingDays = "Saturday,Sunday,Monday,Tuesday,Wednesday"
            };

            _context.SystemSettings.Add(settings);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("تنظیمات سیستم ایجاد شد.");
        }

        /// <summary>
        /// ایجاد سوپرادمین
        /// </summary>
        private async Task CreateSuperAdminAsync(Building building, Job adminJob)
        {
            // ایجاد کارمند سوپرادمین
            var superAdminEmployee = new Employee
            {
                FirstName = "سوپر",
                LastName = "ادمین",
                PersonnelCode = "ADMIN001",
                NationalCode = "**********",
                PhoneNumber = "09123456789",
                BuildingId = building.BuildingId,
                JobId = adminJob.JobId,
                HasDrivingLicense = true,
                IsActive = true
            };

            _context.Employees.Add(superAdminEmployee);
            await _context.SaveChangesAsync();

            // ایجاد کاربر سوپرادمین
            var superAdminUser = new User
            {
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("Admin@123"), // رمز پیش‌فرض
                Role = "Admin",
                IsActive = true,
                EmployeeId = superAdminEmployee.EmployeeId,
                BuildingId = building.BuildingId
            };

            _context.Users.Add(superAdminUser);
            await _context.SaveChangesAsync();

            _logger.LogInformation("سوپرادمین ایجاد شد - نام کاربری: admin، رمز عبور: Admin@123");
        }

        /// <summary>
        /// ایجاد کارمندان نمونه
        /// </summary>
        private async Task CreateSampleEmployeesAsync(Building building, List<Job> jobs)
        {
            var employees = new List<Employee>
            {
                new Employee
                {
                    FirstName = "احمد", LastName = "محمدی", PersonnelCode = "EMP001",
                    NationalCode = "**********", PhoneNumber = "09121111111",
                    BuildingId = building.BuildingId, JobId = jobs.First(j => j.Title == "نگهبان").JobId,
                    HasDrivingLicense = true, IsActive = true
                },
                new Employee
                {
                    FirstName = "فاطمه", LastName = "احمدی", PersonnelCode = "EMP002",
                    NationalCode = "**********", PhoneNumber = "09122222222",
                    BuildingId = building.BuildingId, JobId = jobs.First(j => j.Title == "خزانه‌دار").JobId,
                    HasDrivingLicense = false, IsActive = true
                },
                new Employee
                {
                    FirstName = "علی", LastName = "رضایی", PersonnelCode = "EMP003",
                    NationalCode = "**********", PhoneNumber = "09123333333",
                    BuildingId = building.BuildingId, JobId = jobs.First(j => j.Title == "راننده").JobId,
                    HasDrivingLicense = true, IsActive = true
                },
                new Employee
                {
                    FirstName = "مریم", LastName = "کریمی", PersonnelCode = "EMP004",
                    NationalCode = "**********", PhoneNumber = "09124444444",
                    BuildingId = building.BuildingId, JobId = jobs.First(j => j.Title == "کارمند اداری").JobId,
                    HasDrivingLicense = false, IsActive = true
                }
            };

            _context.Employees.AddRange(employees);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("کارمندان نمونه ایجاد شدند: {EmployeeCount} کارمند", employees.Count);
        }

        /// <summary>
        /// ایجاد خودروهای نمونه
        /// </summary>
        private async Task CreateSampleCarsAsync(Building building)
        {
            var cars = new List<Car>
            {
                new Car
                {
                    PlateNumber = "12ج345678",
                    Model = "پراید",
                    Color = "سفید",
                    Type = "شخصی",
                    PassengerCapacity = 4,
                    IsMoneyTransport = false,
                    BuildingId = building.BuildingId
                },
                new Car
                {
                    PlateNumber = "34د567890",
                    Model = "پیکان",
                    Color = "آبی",
                    Type = "اداری",
                    PassengerCapacity = 4,
                    IsMoneyTransport = false,
                    BuildingId = building.BuildingId
                },
                new Car
                {
                    PlateNumber = "56ه123456",
                    Model = "ون پولرسان",
                    Color = "خاکستری",
                    Type = "پولرسان",
                    PassengerCapacity = 6,
                    IsMoneyTransport = true,
                    BuildingId = building.BuildingId
                }
            };

            _context.Cars.AddRange(cars);
            await _context.SaveChangesAsync();

            // ایجاد وضعیت اولیه خودروها (در پارکینگ)
            foreach (var car in cars)
            {
                var carTrafficLog = new CarTrafficLog
                {
                    CarId = car.CarId,
                    DriverEmployeeId = 1, // سوپرادمین به عنوان ثبت‌کننده اولیه
                    BuildingId = car.BuildingId,
                    EntryTime = DateTime.Now.AddDays(-1), // دیروز وارد شده
                    CurrentStatus = "در پارکینگ",
                    Notes = "وضعیت اولیه - خودرو در پارکینگ"
                };
                _context.CarTrafficLogs.Add(carTrafficLog);
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("خودروهای نمونه ایجاد شدند: {CarCount} خودرو", cars.Count);
        }
    }
}
