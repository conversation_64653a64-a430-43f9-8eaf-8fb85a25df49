using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Users
{
    public class DeleteModel : PageModel
    {
        private readonly UserService _userService;

        public DeleteModel(UserService userService)
        {
            _userService = userService;
        }

        public async Task<IActionResult> OnPostAsync(int id)
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                TempData["ErrorMessage"] = "کاربر مورد نظر یافت نشد.";
                return RedirectToPage("./Index");
            }

            try
            {
                await _userService.DeleteUserAsync(id);
                TempData["SuccessMessage"] = "کاربر با موفقیت حذف شد.";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در حذف کاربر: " + ex.Message;
            }

            return RedirectToPage("./Index");
        }
    }
}
