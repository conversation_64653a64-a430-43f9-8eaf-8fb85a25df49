{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3409"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TcZnpPmw7ljKPx7Q2ghhj5rHDw8GTQovVVgffjnR3YE=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 18:31:45 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TcZnpPmw7ljKPx7Q2ghhj5rHDw8GTQovVVgffjnR3YE="}]}, {"Route": "css/site.rq7e7x16bb.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3409"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TcZnpPmw7ljKPx7Q2ghhj5rHDw8GTQovVVgffjnR3YE=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 18:31:45 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rq7e7x16bb"}, {"Name": "integrity", "Value": "sha256-TcZnpPmw7ljKPx7Q2ghhj5rHDw8GTQovVVgffjnR3YE="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 27 May 2025 19:30:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "js/site.12f8cendp1.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 12:42:02 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "12f8cendp1"}, {"Name": "integrity", "Value": "sha256-OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4267"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc=\""}, {"Name": "Last-Modified", "Value": "Sun, 08 Jun 2025 12:42:02 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OTJPDrijwZ0KX4gjJP4g/vWxgJUZZ9sLWpjYcz5z/vc="}]}]}